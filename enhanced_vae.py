import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class EnhancedEncoder(nn.Module):
    """Enhanced encoder with deeper architecture for better compression."""
    
    def __init__(self, latent_dim=20):
        super(EnhancedEncoder, self).__init__()
        self.latent_dim = latent_dim
        
        # Deeper convolutional layers for better feature extraction
        self.conv1 = nn.Conv2d(1, 64, kernel_size=4, stride=2, padding=1)   # 28x28 -> 14x14
        self.conv2 = nn.Conv2d(64, 128, kernel_size=4, stride=2, padding=1)  # 14x14 -> 7x7
        self.conv3 = nn.Conv2d(128, 256, kernel_size=3, stride=2, padding=1) # 7x7 -> 4x4
        self.conv4 = nn.Conv2d(256, 512, kernel_size=4, stride=1, padding=0) # 4x4 -> 1x1
        
        # Batch normalization for stable training
        self.bn1 = nn.BatchNorm2d(64)
        self.bn2 = nn.BatchNorm2d(128)
        self.bn3 = nn.BatchNorm2d(256)
        self.bn4 = nn.BatchNorm2d(512)
        
        # Fully connected layers for latent space
        self.fc_mu = nn.Linear(512, latent_dim)
        self.fc_logvar = nn.Linear(512, latent_dim)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, x):
        # Add channel dimension if needed
        if len(x.shape) == 3:
            x = x.unsqueeze(1)
        
        # Convolutional layers with batch norm and activation
        x = F.leaky_relu(self.bn1(self.conv1(x)), 0.2)
        x = F.leaky_relu(self.bn2(self.conv2(x)), 0.2)
        x = F.leaky_relu(self.bn3(self.conv3(x)), 0.2)
        x = F.leaky_relu(self.bn4(self.conv4(x)), 0.2)
        
        # Flatten
        x = x.view(x.size(0), -1)
        x = self.dropout(x)
        
        # Get mean and log variance
        mu = self.fc_mu(x)
        logvar = self.fc_logvar(x)
        
        return mu, logvar

class EnhancedDecoder(nn.Module):
    """Enhanced decoder with deeper architecture for better reconstruction."""
    
    def __init__(self, latent_dim=20):
        super(EnhancedDecoder, self).__init__()
        self.latent_dim = latent_dim
        
        # Fully connected layer to expand latent vector
        self.fc = nn.Linear(latent_dim, 512)
        
        # Transposed convolutional layers
        self.deconv1 = nn.ConvTranspose2d(512, 256, kernel_size=4, stride=1, padding=0) # 1x1 -> 4x4
        self.deconv2 = nn.ConvTranspose2d(256, 128, kernel_size=3, stride=2, padding=1) # 4x4 -> 7x7
        self.deconv3 = nn.ConvTranspose2d(128, 64, kernel_size=4, stride=2, padding=1)  # 7x7 -> 14x14
        self.deconv4 = nn.ConvTranspose2d(64, 32, kernel_size=4, stride=2, padding=1)   # 14x14 -> 28x28
        self.deconv5 = nn.ConvTranspose2d(32, 1, kernel_size=3, stride=1, padding=1)    # 28x28 -> 28x28
        
        # Batch normalization
        self.bn1 = nn.BatchNorm2d(256)
        self.bn2 = nn.BatchNorm2d(128)
        self.bn3 = nn.BatchNorm2d(64)
        self.bn4 = nn.BatchNorm2d(32)
        
        # Dropout for regularization
        self.dropout = nn.Dropout(0.2)
        
    def forward(self, z):
        # Expand latent vector
        x = F.leaky_relu(self.fc(z), 0.2)
        x = self.dropout(x)
        x = x.view(x.size(0), 512, 1, 1)
        
        # Transposed convolutional layers
        x = F.leaky_relu(self.bn1(self.deconv1(x)), 0.2)
        x = F.leaky_relu(self.bn2(self.deconv2(x)), 0.2)
        x = F.leaky_relu(self.bn3(self.deconv3(x)), 0.2)
        x = F.leaky_relu(self.bn4(self.deconv4(x)), 0.2)
        x = torch.sigmoid(self.deconv5(x))  # Output in [0, 1]
        
        # Crop to 28x28 if necessary
        if x.size(-1) != 28:
            x = F.interpolate(x, size=(28, 28), mode='bilinear', align_corners=False)
        
        return x.squeeze(1)  # Remove channel dimension

class EnhancedBetaVAE(nn.Module):
    """Enhanced Beta-VAE with improved architecture for high compression ratios."""
    
    def __init__(self, latent_dim=20, beta=1.0):
        super(EnhancedBetaVAE, self).__init__()
        self.latent_dim = latent_dim
        self.beta = beta
        
        self.encoder = EnhancedEncoder(latent_dim)
        self.decoder = EnhancedDecoder(latent_dim)
        
        # Capacity scheduling for progressive training
        self.capacity = 0.0
        self.max_capacity = 25.0
        self.capacity_change_duration = 100000
        self.global_step = 0
        
    def reparameterize(self, mu, logvar):
        """Reparameterization trick with improved numerical stability."""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def forward(self, x):
        # Encode
        mu, logvar = self.encoder(x)
        
        # Reparameterize
        z = self.reparameterize(mu, logvar)
        
        # Decode
        recon_x = self.decoder(z)
        
        return recon_x, mu, logvar, z
    
    def encode(self, x):
        """Encode input to latent space."""
        mu, logvar = self.encoder(x)
        return self.reparameterize(mu, logvar)
    
    def decode(self, z):
        """Decode latent vector to reconstruction."""
        return self.decoder(z)
    
    def loss_function(self, recon_x, x, mu, logvar, step=None):
        """Compute enhanced Beta-VAE loss with capacity scheduling."""
        # Update global step
        if step is not None:
            self.global_step = step
        
        # Ensure inputs are in [0, 1] range
        x = torch.clamp(x, 0, 1)
        recon_x = torch.clamp(recon_x, 0, 1)
        
        # Reconstruction loss (MSE for better gradients)
        recon_loss = F.mse_loss(recon_x, x, reduction='sum')
        
        # KL divergence loss
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
        
        # Update capacity (progressive training)
        if self.global_step < self.capacity_change_duration:
            self.capacity = min(self.max_capacity, 
                              self.max_capacity * self.global_step / self.capacity_change_duration)
        else:
            self.capacity = self.max_capacity
        
        # Apply capacity constraint
        kl_loss = torch.abs(kl_loss - self.capacity)
        
        # Adaptive beta scheduling
        if self.beta > 1.0:
            # For high compression, gradually increase beta
            effective_beta = min(self.beta, 1.0 + (self.beta - 1.0) * min(1.0, self.global_step / 50000))
        else:
            effective_beta = self.beta
        
        # Total loss
        total_loss = recon_loss + effective_beta * kl_loss
        
        return total_loss, recon_loss, kl_loss
    
    def get_compression_ratio(self):
        """Calculate theoretical compression ratio."""
        # Original image: 28 * 28 * 8 bits = 6272 bits
        # Compressed: latent_dim * 32 bits (assuming float32)
        original_bits = 28 * 28 * 8
        compressed_bits = self.latent_dim * 32
        return original_bits / compressed_bits
    
    def set_beta(self, beta):
        """Update beta parameter for different compression ratios."""
        self.beta = beta

def create_enhanced_models(num_models=500):
    """Create model configurations for 500 evaluation points with high compression ratios."""
    configurations = []
    
    # Create a wider range of latent dimensions (1 to 64)
    latent_dims = np.logspace(np.log10(1), np.log10(64), 20).astype(int)
    latent_dims = np.unique(latent_dims)  # Remove duplicates
    
    # Create a wider range of beta values (0.01 to 1000)
    betas = np.logspace(np.log10(0.01), np.log10(1000), 25)
    
    # Generate all combinations
    for latent_dim in latent_dims:
        for beta in betas:
            configurations.append({
                'latent_dim': max(1, int(latent_dim)),  # Ensure at least 1
                'beta': float(beta)
            })
    
    # If we have more than needed, sample evenly
    if len(configurations) > num_models:
        indices = np.linspace(0, len(configurations)-1, num_models).astype(int)
        configurations = [configurations[i] for i in indices]
    
    # If we have fewer, add more by interpolating
    while len(configurations) < num_models:
        # Add intermediate configurations
        for i in range(len(configurations) - 1):
            if len(configurations) >= num_models:
                break
            config1 = configurations[i]
            config2 = configurations[i + 1]
            
            # Interpolate latent dim and beta
            new_latent = int((config1['latent_dim'] + config2['latent_dim']) / 2)
            new_beta = np.sqrt(config1['beta'] * config2['beta'])  # Geometric mean
            
            if new_latent != config1['latent_dim'] and new_latent != config2['latent_dim']:
                configurations.append({
                    'latent_dim': max(1, new_latent),
                    'beta': float(new_beta)
                })
    
    return configurations[:num_models]

if __name__ == "__main__":
    # Test the enhanced model
    print("Testing Enhanced Beta-VAE model...")
    
    # Create model
    model = EnhancedBetaVAE(latent_dim=8, beta=10.0)
    
    # Test with random input
    batch_size = 16
    x = torch.rand(batch_size, 28, 28)
    
    # Forward pass
    recon_x, mu, logvar, z = model(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Latent shape: {z.shape}")
    print(f"Reconstruction shape: {recon_x.shape}")
    print(f"Compression ratio: {model.get_compression_ratio():.2f}x")
    
    # Test loss
    loss, recon_loss, kl_loss = model.loss_function(recon_x, x, mu, logvar)
    print(f"Total loss: {loss.item():.2f}")
    print(f"Reconstruction loss: {recon_loss.item():.2f}")
    print(f"KL loss: {kl_loss.item():.2f}")
    
    # Test model creation
    configs = create_enhanced_models(num_models=10)
    print(f"\nCreated {len(configs)} enhanced configurations")
    for i, config in enumerate(configs[:5]):
        model_test = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        print(f"Model {i+1}: latent_dim={config['latent_dim']}, beta={config['beta']:.3f}, compression={model_test.get_compression_ratio():.1f}x")
    
    # Check maximum compression ratio
    max_compression = max([EnhancedBetaVAE(latent_dim=c['latent_dim']).get_compression_ratio() for c in configs])
    print(f"Maximum compression ratio: {max_compression:.1f}x")
