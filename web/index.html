<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MNIST Compression Analysis - Distortion-Complexity Curves</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://d3js.org/d3.v7.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <h1>MNIST Compression Analysis</h1>
            <p class="subtitle">Interactive Distortion-Complexity Curves using Beta-VAE</p>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Control Panel -->
            <section class="control-panel">
                <h2>Controls</h2>
                <div class="controls">
                    <div class="control-group">
                        <label for="metric-select">Quality Metric:</label>
                        <select id="metric-select">
                            <option value="mse">Mean Squared Error (MSE)</option>
                            <option value="ssim" selected>Structural Similarity (SSIM)</option>
                            <option value="psnr">Peak Signal-to-Noise Ratio (PSNR)</option>
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="compression-range">Compression Range:</label>
                        <div class="range-slider">
                            <input type="range" id="compression-min" min="2" max="50" value="2" step="1">
                            <input type="range" id="compression-max" min="2" max="50" value="50" step="1">
                        </div>
                        <div class="range-values">
                            <span id="compression-min-value">2x</span> - 
                            <span id="compression-max-value">50x</span>
                        </div>
                    </div>
                    
                    <div class="control-group">
                        <button id="reset-zoom" class="btn">Reset Zoom</button>
                        <button id="export-data" class="btn">Export Data</button>
                    </div>
                </div>
            </section>

            <!-- Distortion Curve Visualization -->
            <section class="curve-section">
                <h2>Distortion-Complexity Curve</h2>
                <div class="chart-container">
                    <div id="distortion-chart"></div>
                    <div class="chart-info">
                        <p>Hover over points to see details. Click to view reconstructions.</p>
                        <div id="point-info" class="point-info"></div>
                    </div>
                </div>
            </section>

            <!-- Image Comparison Section -->
            <section class="image-section">
                <h2>Image Reconstructions</h2>
                <div class="image-controls">
                    <div class="control-group">
                        <label for="image-select">Select Image:</label>
                        <select id="image-select">
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                    
                    <div class="control-group">
                        <label for="model-select">Compression Level:</label>
                        <select id="model-select">
                            <!-- Options will be populated by JavaScript -->
                        </select>
                    </div>
                </div>
                
                <div class="image-comparison">
                    <div class="image-pair">
                        <div class="image-container">
                            <h3>Original</h3>
                            <canvas id="original-canvas" width="280" height="280"></canvas>
                            <div class="image-info">
                                <p>Size: <span id="original-size">6,272 bits</span></p>
                                <p>Label: <span id="image-label">-</span></p>
                            </div>
                        </div>
                        
                        <div class="image-container">
                            <h3>Reconstructed</h3>
                            <canvas id="reconstructed-canvas" width="280" height="280"></canvas>
                            <div class="image-info">
                                <p>Size: <span id="compressed-size">- bits</span></p>
                                <p>Compression: <span id="compression-ratio">-x</span></p>
                                <p>Quality: <span id="quality-metric">-</span></p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="stats-section">
                <h2>Analysis Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Models Trained</h3>
                        <div class="stat-value" id="models-count">-</div>
                        <p>Different compression configurations</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Compression Range</h3>
                        <div class="stat-value" id="compression-range-stat">-</div>
                        <p>From lossless to highly compressed</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Best Quality</h3>
                        <div class="stat-value" id="best-quality">-</div>
                        <p>Highest SSIM achieved</p>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Best Compression</h3>
                        <div class="stat-value" id="best-compression">-</div>
                        <p>Highest compression ratio</p>
                    </div>
                </div>
            </section>

            <!-- Model Details Section -->
            <section class="details-section">
                <h2>Model Architecture</h2>
                <div class="architecture-info">
                    <div class="arch-card">
                        <h3>Beta-VAE</h3>
                        <p>Variational Autoencoder with controllable β parameter for balancing reconstruction quality and compression ratio.</p>
                        <ul>
                            <li>Encoder: 3 convolutional layers</li>
                            <li>Latent space: Variable dimensions (4-64)</li>
                            <li>Decoder: 4 transposed convolutional layers</li>
                            <li>β range: 0.1 - 100</li>
                        </ul>
                    </div>
                    
                    <div class="arch-card">
                        <h3>Training Details</h3>
                        <p>Models trained on MNIST dataset with different configurations to explore the rate-distortion trade-off.</p>
                        <ul>
                            <li>Dataset: 60,000 training images</li>
                            <li>Epochs: 15-20 per model</li>
                            <li>Optimizer: Adam</li>
                            <li>Loss: Reconstruction + β × KL divergence</li>
                        </ul>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2025 MNIST Compression Analysis. Built with D3.js and modern web technologies.</p>
        </footer>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner"></div>
        <p>Loading compression analysis data...</p>
    </div>

    <!-- Scripts -->
    <script src="script.js"></script>
</body>
</html>
