[{"model_id": 0, "latent_dim": 4, "beta": 0.1, "compression_ratio": 2.0, "bits_per_pixel": 4.0, "compressed_bits": 3136, "mse": 0.001, "ssim": 0.9197029434626006, "psnr": 33.81752864385294, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 1, "latent_dim": 4, "beta": 0.2998, "compression_ratio": 2.022252992090197, "bits_per_pixel": 3.955983762314138, "compressed_bits": 3101, "mse": 0.001, "ssim": 0.9015654338829249, "psnr": 34.52326917950764, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 2, "latent_dim": 4, "beta": 0.49960000000000004, "compression_ratio": 2.044753582008878, "bits_per_pixel": 3.9124518819232788, "compressed_bits": 3067, "mse": 0.004728818698874935, "ssim": 0.9198212455402325, "psnr": 35.60421934193843, "train_recon_loss": 4.728818698874934, "test_recon_loss": 5.201700568762428}, {"model_id": 3, "latent_dim": 4, "beta": 0.6994, "compression_ratio": 2.0675045246523007, "bits_per_pixel": 3.8693990289309705, "compressed_bits": 3033, "mse": 0.004380245599940002, "ssim": 0.9284930852176176, "psnr": 33.55233515319897, "train_recon_loss": 4.380245599940002, "test_recon_loss": 4.818270159934002}, {"model_id": 4, "latent_dim": 4, "beta": 0.8992, "compression_ratio": 2.090508605569068, "bits_per_pixel": 3.8268199320912526, "compressed_bits": 3000, "mse": 0.001, "ssim": 0.9607636379597765, "psnr": 34.20610623949608, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 5, "latent_dim": 4, "beta": 1.099, "compression_ratio": 2.1137686413011774, "bits_per_pixel": 3.7847093781632704, "compressed_bits": 2967, "mse": 0.001, "ssim": 0.9252027238300101, "psnr": 33.60433995479568, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 6, "latent_dim": 4, "beta": 1.2988000000000002, "compression_ratio": 2.1372874797288683, "bits_per_pixel": 3.7430622112729837, "compressed_bits": 2934, "mse": 0.001, "ssim": 0.9233718673612338, "psnr": 34.94882486358173, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 7, "latent_dim": 4, "beta": 1.4986000000000002, "compression_ratio": 2.16106800041931, "bits_per_pixel": 3.7018733322818935, "compressed_bits": 2902, "mse": 0.0023122282046322854, "ssim": 0.9338778349427151, "psnr": 34.89529166182638, "train_recon_loss": 2.3122282046322855, "test_recon_loss": 2.543451025095514}, {"model_id": 8, "latent_dim": 4, "beta": 1.6984000000000001, "compression_ratio": 2.185113114979165, "bits_per_pixel": 3.6611376981627237, "compressed_bits": 2870, "mse": 0.001, "ssim": 0.9181886876859318, "psnr": 34.461028911558415, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 9, "latent_dim": 5, "beta": 1.8982, "compression_ratio": 2.209425767411074, "bits_per_pixel": 3.620850321381973, "compressed_bits": 2838, "mse": 0.0018195514137556267, "ssim": 0.9229740033454525, "psnr": 33.72451898151573, "train_recon_loss": 1.8195514137556268, "test_recon_loss": 2.0015065551311895}, {"model_id": 10, "latent_dim": 5, "beta": 2.098, "compression_ratio": 2.234008934474112, "bits_per_pixel": 3.5810062692892535, "compressed_bits": 2807, "mse": 0.006697259738238917, "ssim": 0.9202102953092272, "psnr": 33.92467780611885, "train_recon_loss": 6.697259738238917, "test_recon_loss": 7.366985712062808}, {"model_id": 11, "latent_dim": 5, "beta": 2.2978, "compression_ratio": 2.258865626048254, "bits_per_pixel": 3.5416006635133526, "compressed_bits": 2776, "mse": 0.013566755500467332, "ssim": 0.9382605854284467, "psnr": 34.597219920502944, "train_recon_loss": 13.566755500467332, "test_recon_loss": 14.923431050514065}, {"model_id": 12, "latent_dim": 5, "beta": 2.4976000000000003, "compression_ratio": 2.2839988855028888, "bits_per_pixel": 3.5026286793649497, "compressed_bits": 2746, "mse": 0.001, "ssim": 0.9167935187879785, "psnr": 34.45506220571495, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 13, "latent_dim": 5, "beta": 2.6974, "compression_ratio": 2.309411790069446, "bits_per_pixel": 3.4640855452458883, "compressed_bits": 2715, "mse": 0.001, "ssim": 0.8926546269064323, "psnr": 34.462259620267105, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 14, "latent_dim": 5, "beta": 2.8972, "compression_ratio": 2.335107451218158, "bits_per_pixel": 3.4259665420649617, "compressed_bits": 2685, "mse": 0.001, "ssim": 0.9505883908374921, "psnr": 35.88175705533876, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 15, "latent_dim": 5, "beta": 3.097, "compression_ratio": 2.3610890150390174, "bits_per_pixel": 3.388267002660126, "compressed_bits": 2656, "mse": 0.006601995428149736, "ssim": 0.8861058250535733, "psnr": 35.30072969460485, "train_recon_loss": 6.601995428149737, "test_recon_loss": 7.2621949709647104}, {"model_id": 16, "latent_dim": 5, "beta": 3.2968, "compression_ratio": 2.387359662626975, "bits_per_pixel": 3.350982311227062, "compressed_bits": 2627, "mse": 0.001, "ssim": 0.9252644845075126, "psnr": 33.49901474006986, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 17, "latent_dim": 6, "beta": 3.4966000000000004, "compression_ratio": 2.4139226104714218, "bits_per_pixel": 3.3141079027540394, "compressed_bits": 2598, "mse": 0.001, "ssim": 0.884885944070783, "psnr": 35.76494770615703, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 18, "latent_dim": 6, "beta": 3.6964, "compression_ratio": 2.4407811108500064, "bits_per_pixel": 3.277639262462985, "compressed_bits": 2569, "mse": 0.005885799941660729, "ssim": 0.9166988495462891, "psnr": 35.88818525094284, "train_recon_loss": 5.885799941660729, "test_recon_loss": 6.474379935826802}, {"model_id": 19, "latent_dim": 6, "beta": 3.8962000000000003, "compression_ratio": 2.4679384522268304, "bits_per_pixel": 3.2415719252567134, "compressed_bits": 2541, "mse": 0.006180440456255405, "ssim": 0.9477833500346142, "psnr": 34.60084018908973, "train_recon_loss": 6.180440456255405, "test_recon_loss": 6.798484501880945}, {"model_id": 20, "latent_dim": 6, "beta": 4.096, "compression_ratio": 2.495397959655079, "bits_per_pixel": 3.205901475172234, "compressed_bits": 2513, "mse": 0.005330161451246991, "ssim": 0.9306399595303875, "psnr": 35.00681631112601, "train_recon_loss": 5.330161451246991, "test_recon_loss": 5.86317759637169}, {"model_id": 21, "latent_dim": 6, "beta": 4.2958, "compression_ratio": 2.5231629951841286, "bits_per_pixel": 3.170623544840074, "compressed_bits": 2485, "mse": 0.013554709460223701, "ssim": 0.9168442645488231, "psnr": 31.900377366171963, "train_recon_loss": 13.554709460223702, "test_recon_loss": 14.910180406246072}, {"model_id": 22, "latent_dim": 6, "beta": 4.4956, "compression_ratio": 2.5512369582711845, "bits_per_pixel": 3.135733814949555, "compressed_bits": 2458, "mse": 0.006477113349881744, "ssim": 0.9157294184585743, "psnr": 34.72776562640484, "train_recon_loss": 6.477113349881744, "test_recon_loss": 7.124824684869918}, {"model_id": 23, "latent_dim": 6, "beta": 4.6954, "compression_ratio": 2.579623286197498, "bits_per_pixel": 3.1012280137199513, "compressed_bits": 2431, "mse": 0.009993398730220648, "ssim": 0.9202361274694377, "psnr": 33.48083874279253, "train_recon_loss": 9.993398730220648, "test_recon_loss": 10.992738603242714}, {"model_id": 24, "latent_dim": 6, "beta": 4.8952, "compression_ratio": 2.608325454489219, "bits_per_pixel": 3.067101916377463, "compressed_bits": 2404, "mse": 0.0016731272697587346, "ssim": 0.9050065530286276, "psnr": 34.58226671982029, "train_recon_loss": 1.6731272697587347, "test_recon_loss": 1.8404399967346081}, {"model_id": 25, "latent_dim": 7, "beta": 5.095, "compression_ratio": 2.6373469773429234, "bits_per_pixel": 3.033351344637954, "compressed_bits": 2378, "mse": 0.0013977103024113522, "ssim": 0.9388489949385219, "psnr": 36.525025012855956, "train_recon_loss": 1.3977103024113522, "test_recon_loss": 1.5374813326524874}, {"model_id": 26, "latent_dim": 7, "beta": 5.2947999999999995, "compression_ratio": 2.666691408055882, "bits_per_pixel": 2.9999721661953753, "compressed_bits": 2351, "mse": 0.01312012954546284, "ssim": 0.9499774898984018, "psnr": 34.84829568868429, "train_recon_loss": 13.12012954546284, "test_recon_loss": 14.432142500009123}, {"model_id": 27, "latent_dim": 7, "beta": 5.4946, "compression_ratio": 2.6963623394611145, "bits_per_pixel": 2.966960294215818, "compressed_bits": 2326, "mse": 0.001, "ssim": 0.9244102520336372, "psnr": 34.78345407376152, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 28, "latent_dim": 7, "beta": 5.6944, "compression_ratio": 2.7263634043672815, "bits_per_pixel": 2.934311686837138, "compressed_bits": 2300, "mse": 0.004172373278033646, "ssim": 0.9086309780477871, "psnr": 35.98471873393941, "train_recon_loss": 4.172373278033645, "test_recon_loss": 4.58961060583701}, {"model_id": 29, "latent_dim": 7, "beta": 5.8942000000000005, "compression_ratio": 2.7566982760034757, "bits_per_pixel": 2.902022346674081, "compressed_bits": 2275, "mse": 0.001, "ssim": 0.9422466637673989, "psnr": 33.69533906723748, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 30, "latent_dim": 7, "beta": 6.093999999999999, "compression_ratio": 2.7873706684689585, "bits_per_pixel": 2.870088320328858, "compressed_bits": 2250, "mse": 0.005953856914283887, "ssim": 0.8915453563144758, "psnr": 34.41504827509589, "train_recon_loss": 5.953856914283887, "test_recon_loss": 6.5492426057122755}, {"model_id": 31, "latent_dim": 7, "beta": 6.2938, "compression_ratio": 2.8183843371879025, "bits_per_pixel": 2.8385056979071046, "compressed_bits": 2225, "mse": 0.001, "ssim": 0.9344343784236122, "psnr": 34.26627116140567, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 32, "latent_dim": 7, "beta": 6.4936, "compression_ratio": 2.849743079369192, "bits_per_pixel": 2.807270612539166, "compressed_bits": 2200, "mse": 0.0012763048755447832, "ssim": 0.9036253802408131, "psnr": 36.167356673687536, "train_recon_loss": 1.2763048755447832, "test_recon_loss": 1.4039353630992615}, {"model_id": 33, "latent_dim": 7, "beta": 6.6934000000000005, "compression_ratio": 2.8814507344713403, "bits_per_pixel": 2.7763792399066505, "compressed_bits": 2176, "mse": 0.0026569893211465705, "ssim": 0.9415212099695279, "psnr": 34.074250365972944, "train_recon_loss": 2.6569893211465705, "test_recon_loss": 2.9226882532612275}, {"model_id": 34, "latent_dim": 8, "beta": 6.8932, "compression_ratio": 2.9135111846725823, "bits_per_pixel": 2.745827797774194, "compressed_bits": 2152, "mse": 0.006425884293135264, "ssim": 0.9324458865534497, "psnr": 34.50449610000688, "train_recon_loss": 6.425884293135264, "test_recon_loss": 7.06847272244879}, {"model_id": 35, "latent_dim": 8, "beta": 7.093, "compression_ratio": 2.9459283553461924, "bits_per_pixel": 2.715612545526375, "compressed_bits": 2129, "mse": 0.001, "ssim": 0.9284638777470671, "psnr": 32.93298469386084, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 36, "latent_dim": 8, "beta": 7.2928, "compression_ratio": 2.978706215541096, "bits_per_pixel": 2.6857297837097245, "compressed_bits": 2105, "mse": 0.004612113798542155, "ssim": 0.9169732638804707, "psnr": 31.69563797538813, "train_recon_loss": 4.612113798542155, "test_recon_loss": 5.073325178396371}, {"model_id": 37, "latent_dim": 8, "beta": 7.4926, "compression_ratio": 3.011848778467825, "bits_per_pixel": 2.6561758535797826, "compressed_bits": 2082, "mse": 0.0021358013591093214, "ssim": 0.9475932083241498, "psnr": 34.14784445582982, "train_recon_loss": 2.1358013591093212, "test_recon_loss": 2.3493814950202534}, {"model_id": 38, "latent_dim": 8, "beta": 7.6924, "compression_ratio": 3.045360101989882, "bits_per_pixel": 2.6269471366531287, "compressed_bits": 2059, "mse": 0.005075166645195641, "ssim": 0.9295982200774099, "psnr": 33.10417553943367, "train_recon_loss": 5.07516664519564, "test_recon_loss": 5.582683309715205}, {"model_id": 39, "latent_dim": 8, "beta": 7.892200000000001, "compression_ratio": 3.079244289120574, "bits_per_pixel": 2.5980400542643483, "compressed_bits": 2036, "mse": 0.004663609884348209, "ssim": 0.9518669196241892, "psnr": 33.59827981756681, "train_recon_loss": 4.663609884348209, "test_recon_loss": 5.12997087278303}, {"model_id": 40, "latent_dim": 8, "beta": 8.092, "compression_ratio": 3.1135054885253663, "bits_per_pixel": 2.5694510671278756, "compressed_bits": 2014, "mse": 0.005869000500034017, "ssim": 0.9334135088097485, "psnr": 36.82063929164065, "train_recon_loss": 5.869000500034017, "test_recon_loss": 6.455900550037419}, {"model_id": 41, "latent_dim": 8, "beta": 8.2918, "compression_ratio": 3.1481478950298367, "bits_per_pixel": 2.541176674904652, "compressed_bits": 1992, "mse": 0.0019776984593406583, "ssim": 0.9693108418013782, "psnr": 33.04267872803045, "train_recon_loss": 1.9776984593406584, "test_recon_loss": 2.175468305274724}, {"model_id": 42, "latent_dim": 9, "beta": 8.4916, "compression_ratio": 3.183175750133272, "bits_per_pixel": 2.5132134157735586, "compressed_bits": 1970, "mse": 0.001, "ssim": 0.9253409397174426, "psnr": 34.3779092634857, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 43, "latent_dim": 9, "beta": 8.6914, "compression_ratio": 3.218593342527984, "bits_per_pixel": 2.4855578660075612, "compressed_bits": 1948, "mse": 0.0030748109887905224, "ssim": 0.9069717384109527, "psnr": 34.36364160482671, "train_recon_loss": 3.0748109887905226, "test_recon_loss": 3.382292087669575}, {"model_id": 44, "latent_dim": 9, "beta": 8.8912, "compression_ratio": 3.254405008624402, "bits_per_pixel": 2.4582066395545232, "compressed_bits": 1927, "mse": 0.001, "ssim": 0.9106773768967154, "psnr": 35.064696575106886, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 45, "latent_dim": 9, "beta": 9.091, "compression_ratio": 3.2906151330820106, "bits_per_pixel": 2.431156387622624, "compressed_bits": 1906, "mse": 0.0026195749561499612, "ssim": 0.9367954665978722, "psnr": 36.92525243937179, "train_recon_loss": 2.619574956149961, "test_recon_loss": 2.8815324517649574}, {"model_id": 46, "latent_dim": 9, "beta": 9.2908, "compression_ratio": 3.32722814934619, "bits_per_pixel": 2.404403798270348, "compressed_bits": 1885, "mse": 0.0015208910114787452, "ssim": 0.950321599773856, "psnr": 32.954775738950666, "train_recon_loss": 1.520891011478745, "test_recon_loss": 1.6729801126266197}, {"model_id": 47, "latent_dim": 9, "beta": 9.4906, "compression_ratio": 3.3642485401910305, "bits_per_pixel": 2.377945596000984, "compressed_bits": 1864, "mse": 0.003919984034951227, "ssim": 0.9427779863757368, "psnr": 35.56787924162663, "train_recon_loss": 3.919984034951227, "test_recon_loss": 4.31198243844635}, {"model_id": 48, "latent_dim": 9, "beta": 9.6904, "compression_ratio": 3.401680838268195, "bits_per_pixel": 2.3517785413615764, "compressed_bits": 1843, "mse": 0.001, "ssim": 0.918320105156307, "psnr": 33.680693495640675, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 49, "latent_dim": 9, "beta": 9.8902, "compression_ratio": 3.4395296266618742, "bits_per_pixel": 2.3258994305463054, "compressed_bits": 1823, "mse": 0.0043111491068210175, "ssim": 0.9353323030224555, "psnr": 35.838082939225515, "train_recon_loss": 4.311149106821017, "test_recon_loss": 4.742264017503119}, {"model_id": 50, "latent_dim": 10, "beta": 10.09, "compression_ratio": 3.4777995394499266, "bits_per_pixel": 2.300305095004221, "compressed_bits": 1803, "mse": 0.009576275207452092, "ssim": 0.9616675335484186, "psnr": 34.04512198529459, "train_recon_loss": 9.576275207452092, "test_recon_loss": 10.533902728197301}, {"model_id": 51, "latent_dim": 10, "beta": 10.289800000000001, "compression_ratio": 3.5164952622712624, "bits_per_pixel": 2.274992401051294, "compressed_bits": 1783, "mse": 0.001, "ssim": 0.9607646308055394, "psnr": 32.40512651181807, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 52, "latent_dim": 10, "beta": 10.4896, "compression_ratio": 3.5556215328995324, "bits_per_pixel": 2.2499582494867427, "compressed_bits": 1763, "mse": 0.0020761620433141473, "ssim": 0.9076912257108647, "psnr": 35.70852092243207, "train_recon_loss": 2.0761620433141474, "test_recon_loss": 2.283778247645562}, {"model_id": 53, "latent_dim": 10, "beta": 10.689400000000001, "compression_ratio": 3.5951831418232056, "bits_per_pixel": 2.225199575213574, "compressed_bits": 1744, "mse": 0.001216982460434162, "ssim": 0.9539324873719309, "psnr": 35.46529256981303, "train_recon_loss": 1.2169824604341621, "test_recon_loss": 1.3386807064775783}, {"model_id": 54, "latent_dim": 10, "beta": 10.8892, "compression_ratio": 3.6351849328321073, "bits_per_pixel": 2.2007133468633033, "compressed_bits": 1725, "mse": 0.001, "ssim": 0.9385902640884799, "psnr": 33.82425374440279, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 55, "latent_dim": 10, "beta": 11.089, "compression_ratio": 3.6756318036104663, "bits_per_pixel": 2.176496566424807, "compressed_bits": 1706, "mse": 0.001, "ssim": 0.9356344834993908, "psnr": 34.59782396550424, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 56, "latent_dim": 10, "beta": 11.2888, "compression_ratio": 3.716528706336576, "bits_per_pixel": 2.1525462688772525, "compressed_bits": 1687, "mse": 0.0016573570170368983, "ssim": 0.927446142267329, "psnr": 33.824008668444826, "train_recon_loss": 1.6573570170368983, "test_recon_loss": 1.823092718740588}, {"model_id": 57, "latent_dim": 10, "beta": 11.4886, "compression_ratio": 3.757880648289126, "bits_per_pixel": 2.128859521827073, "compressed_bits": 1669, "mse": 0.004359481633847722, "ssim": 0.9326307413114429, "psnr": 32.04105032232936, "train_recon_loss": 4.359481633847722, "test_recon_loss": 4.795429797232494}, {"model_id": 58, "latent_dim": 10, "beta": 11.688400000000001, "compression_ratio": 3.799692692460268, "bits_per_pixel": 2.105433425148935, "compressed_bits": 1650, "mse": 0.010045351919428862, "ssim": 0.8898860935529447, "psnr": 35.945963887098344, "train_recon_loss": 10.045351919428862, "test_recon_loss": 11.04988711137175}, {"model_id": 59, "latent_dim": 11, "beta": 11.888200000000001, "compression_ratio": 3.841969958175517, "bits_per_pixel": 2.0822651106306562, "compressed_bits": 1632, "mse": 0.0034525506337459984, "ssim": 0.9574519708137188, "psnr": 34.75939707841555, "train_recon_loss": 3.4525506337459984, "test_recon_loss": 3.797805697120598}, {"model_id": 60, "latent_dim": 11, "beta": 12.088, "compression_ratio": 3.884717621720545, "bits_per_pixel": 2.0593517416220313, "compressed_bits": 1614, "mse": 0.001, "ssim": 0.9364507569088466, "psnr": 34.7462326614197, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 61, "latent_dim": 11, "beta": 12.2878, "compression_ratio": 3.927940916974944, "bits_per_pixel": 2.0366905126875237, "compressed_bits": 1596, "mse": 0.005687076956530863, "ssim": 0.9366479625501284, "psnr": 33.9293123441869, "train_recon_loss": 5.687076956530864, "test_recon_loss": 6.25578465218395}, {"model_id": 62, "latent_dim": 11, "beta": 12.4876, "compression_ratio": 3.9716451360530463, "bits_per_pixel": 2.014278649262775, "compressed_bits": 1579, "mse": 0.00239771022382374, "ssim": 0.9349862199278255, "psnr": 33.86854204633501, "train_recon_loss": 2.39771022382374, "test_recon_loss": 2.637481246206114}, {"model_id": 63, "latent_dim": 11, "beta": 12.6874, "compression_ratio": 4.0158356299518765, "bits_per_pixel": 1.9921134073148974, "compressed_bits": 1561, "mse": 0.001, "ssim": 0.9178038785869808, "psnr": 33.8680497056285, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 64, "latent_dim": 11, "beta": 12.8872, "compression_ratio": 4.060517809206302, "bits_per_pixel": 1.970192073006506, "compressed_bits": 1544, "mse": 0.001, "ssim": 0.9120240219023527, "psnr": 34.03043002798469, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 65, "latent_dim": 11, "beta": 13.087, "compression_ratio": 4.105697144551488, "bits_per_pixel": 1.9485119623634417, "compressed_bits": 1527, "mse": 0.001, "ssim": 0.9465808139769579, "psnr": 34.18356520221606, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 66, "latent_dim": 11, "beta": 13.286800000000001, "compression_ratio": 4.151379167592713, "bits_per_pixel": 1.9270704209461578, "compressed_bits": 1510, "mse": 0.001, "ssim": 0.891793214889441, "psnr": 34.60685354252617, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 67, "latent_dim": 12, "beta": 13.4866, "compression_ratio": 4.197569471482639, "bits_per_pixel": 1.905864823524717, "compressed_bits": 1494, "mse": 0.00918520106436706, "ssim": 0.8746271945984048, "psnr": 36.790939148972434, "train_recon_loss": 9.18520106436706, "test_recon_loss": 10.103721170803766}, {"model_id": 68, "latent_dim": 12, "beta": 13.6864, "compression_ratio": 4.244273711606117, "bits_per_pixel": 1.8848925737573703, "compressed_bits": 1477, "mse": 0.006455140708427569, "ssim": 0.9129955072928021, "psnr": 35.525135126772454, "train_recon_loss": 6.455140708427569, "test_recon_loss": 7.100654779270326}, {"model_id": 69, "latent_dim": 12, "beta": 13.8862, "compression_ratio": 4.2914976062726184, "bits_per_pixel": 1.8641511038726648, "compressed_bits": 1461, "mse": 0.001, "ssim": 0.9354147628807555, "psnr": 35.834681303868365, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 70, "latent_dim": 12, "beta": 14.086, "compression_ratio": 4.339246937416362, "bits_per_pixel": 1.8436378743550588, "compressed_bits": 1445, "mse": 0.0032053317274840327, "ssim": 0.9180225116860615, "psnr": 33.38957162200379, "train_recon_loss": 3.2053317274840327, "test_recon_loss": 3.525864900232436}, {"model_id": 71, "latent_dim": 12, "beta": 14.2858, "compression_ratio": 4.38752755130423, "bits_per_pixel": 1.8233503736339916, "compressed_bits": 1429, "mse": 0.001, "ssim": 0.95853508316874, "psnr": 33.95963056561036, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 72, "latent_dim": 12, "beta": 14.4856, "compression_ratio": 4.436345359251578, "bits_per_pixel": 1.8032861177763715, "compressed_bits": 1413, "mse": 0.001, "ssim": 0.8911807630311274, "psnr": 36.57414429427539, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 73, "latent_dim": 12, "beta": 14.685400000000001, "compression_ratio": 4.485706338345984, "bits_per_pixel": 1.7834426501824556, "compressed_bits": 1398, "mse": 0.001, "ssim": 0.9053022642156426, "psnr": 35.08245378730135, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 74, "latent_dim": 12, "beta": 14.885200000000001, "compression_ratio": 4.535616532179064, "bits_per_pixel": 1.7638175412850718, "compressed_bits": 1382, "mse": 0.00567711728104779, "ssim": 0.9323846155524926, "psnr": 34.15983465864001, "train_recon_loss": 5.6771172810477895, "test_recon_loss": 6.244829009152569}, {"model_id": 75, "latent_dim": 13, "beta": 15.084999999999999, "compression_ratio": 4.586082051586437, "bits_per_pixel": 1.744408388252148, "compressed_bits": 1367, "mse": 0.009811047045673892, "ssim": 0.9087366019814926, "psnr": 34.20666533426698, "train_recon_loss": 9.811047045673892, "test_recon_loss": 10.792151750241281}, {"model_id": 76, "latent_dim": 13, "beta": 15.2848, "compression_ratio": 4.637109075395913, "bits_per_pixel": 1.7252128146925174, "compressed_bits": 1352, "mse": 0.005089575095255653, "ssim": 0.8975023787275971, "psnr": 34.47497817508073, "train_recon_loss": 5.089575095255653, "test_recon_loss": 5.5985326047812185}, {"model_id": 77, "latent_dim": 13, "beta": 15.4846, "compression_ratio": 4.688703851183996, "bits_per_pixel": 1.7062284703649673, "compressed_bits": 1337, "mse": 0.001, "ssim": 0.8919902967824017, "psnr": 33.63377427121404, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 78, "latent_dim": 13, "beta": 15.684400000000002, "compression_ratio": 4.7408726960408325, "bits_per_pixel": 1.687453030890475, "compressed_bits": 1322, "mse": 0.009467039644561501, "ssim": 0.8970759548566936, "psnr": 34.51915550267113, "train_recon_loss": 9.4670396445615, "test_recon_loss": 10.41374360901765}, {"model_id": 79, "latent_dim": 13, "beta": 15.8842, "compression_ratio": 4.7936219973436485, "bits_per_pixel": 1.6688841974676232, "compressed_bits": 1308, "mse": 0.009700616522381689, "ssim": 0.9066327763095077, "psnr": 34.60484677114727, "train_recon_loss": 9.700616522381688, "test_recon_loss": 10.670678174619857}, {"model_id": 80, "latent_dim": 13, "beta": 16.084, "compression_ratio": 4.84695821353879, "bits_per_pixel": 1.6505196965911446, "compressed_bits": 1294, "mse": 0.0058350153848974705, "ssim": 0.909475604074955, "psnr": 33.27372124633262, "train_recon_loss": 5.835015384897471, "test_recon_loss": 6.418516923387218}, {"model_id": 81, "latent_dim": 13, "beta": 16.283800000000003, "compression_ratio": 4.9008878749324865, "bits_per_pixel": 1.6323572797735566, "compressed_bits": 1279, "mse": 0.001, "ssim": 0.9088556998649286, "psnr": 35.54590181184498, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 82, "latent_dim": 13, "beta": 16.483600000000003, "compression_ratio": 4.955417584490397, "bits_per_pixel": 1.6143947232698654, "compressed_bits": 1265, "mse": 0.001, "ssim": 0.9212972176311283, "psnr": 33.516031915323495, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 83, "latent_dim": 13, "beta": 16.683400000000002, "compression_ratio": 5.01055401864604, "bits_per_pixel": 1.5966298278053037, "compressed_bits": 1251, "mse": 0.001, "ssim": 0.909688759550037, "psnr": 34.28425982990868, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 84, "latent_dim": 14, "beta": 16.883200000000002, "compression_ratio": 5.066303928118258, "bits_per_pixel": 1.5790604183060497, "compressed_bits": 1237, "mse": 0.001, "ssim": 0.9255912897092345, "psnr": 35.42783735818237, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 85, "latent_dim": 14, "beta": 17.083000000000002, "compression_ratio": 5.122674138737735, "bits_per_pixel": 1.561684343632925, "compressed_bits": 1224, "mse": 0.010948723457468024, "ssim": 0.8790177701775236, "psnr": 34.12592359926772, "train_recon_loss": 10.948723457468024, "test_recon_loss": 12.043595803214826}, {"model_id": 86, "latent_dim": 14, "beta": 17.2828, "compression_ratio": 5.179671552282729, "bits_per_pixel": 1.544499476318016, "compressed_bits": 1210, "mse": 0.001459886310835896, "ssim": 0.931026238313036, "psnr": 35.61093558208488, "train_recon_loss": 1.459886310835896, "test_recon_loss": 1.6058749419194855}, {"model_id": 87, "latent_dim": 14, "beta": 17.482600000000005, "compression_ratio": 5.237303147324112, "bits_per_pixel": 1.5275037123041901, "compressed_bits": 1197, "mse": 0.002250143338843503, "ssim": 0.9075120397356593, "psnr": 32.81808276565313, "train_recon_loss": 2.250143338843503, "test_recon_loss": 2.4751576727278537}, {"model_id": 88, "latent_dim": 14, "beta": 17.6824, "compression_ratio": 5.295575980079798, "bits_per_pixel": 1.510694970687485, "compressed_bits": 1184, "mse": 0.0010691742883900963, "ssim": 0.886832214611844, "psnr": 34.0834640632437, "train_recon_loss": 1.0691742883900963, "test_recon_loss": 1.1760917172291059}, {"model_id": 89, "latent_dim": 14, "beta": 17.8822, "compression_ratio": 5.3544971852786745, "bits_per_pixel": 1.4940711934623307, "compressed_bits": 1171, "mse": 0.001, "ssim": 0.9052572080497066, "psnr": 33.45931714136095, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 90, "latent_dim": 14, "beta": 18.082, "compression_ratio": 5.414073977034168, "bits_per_pixel": 1.4776303452695714, "compressed_bits": 1158, "mse": 0.008707587837793181, "ssim": 0.9296236663479625, "psnr": 34.08482283712407, "train_recon_loss": 8.70758783779318, "test_recon_loss": 9.5783466215725}, {"model_id": 91, "latent_dim": 14, "beta": 18.2818, "compression_ratio": 5.474313649727511, "bits_per_pixel": 1.461370413147264, "compressed_bits": 1145, "mse": 0.01215578043251552, "ssim": 0.9422763574493054, "psnr": 34.78338373023421, "train_recon_loss": 12.15578043251552, "test_recon_loss": 13.371358475767071}, {"model_id": 92, "latent_dim": 15, "beta": 18.481600000000004, "compression_ratio": 5.535223578900834, "bits_per_pixel": 1.4452894062842196, "compressed_bits": 1133, "mse": 0.0075367267388276255, "ssim": 0.9003685956116009, "psnr": 34.06625649193913, "train_recon_loss": 7.536726738827626, "test_recon_loss": 8.290399412710387}, {"model_id": 93, "latent_dim": 15, "beta": 18.681400000000004, "compression_ratio": 5.596811222160211, "bits_per_pixel": 1.4293853557762533, "compressed_bits": 1120, "mse": 0.00866670730062474, "ssim": 0.9090356627083751, "psnr": 32.73100171450733, "train_recon_loss": 8.66670730062474, "test_recon_loss": 9.533378030687214}, {"model_id": 94, "latent_dim": 15, "beta": 18.881200000000003, "compression_ratio": 5.659084120088741, "bits_per_pixel": 1.4136563143851184, "compressed_bits": 1108, "mse": 0.006241030556689044, "ssim": 0.9152079661118162, "psnr": 34.44221309393041, "train_recon_loss": 6.241030556689044, "test_recon_loss": 6.865133612357948}, {"model_id": 95, "latent_dim": 15, "beta": 19.081000000000003, "compression_ratio": 5.722049897169788, "bits_per_pixel": 1.3981003563000944, "compressed_bits": 1096, "mse": 0.001, "ssim": 0.9298164346169507, "psnr": 33.13394026843959, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 96, "latent_dim": 15, "beta": 19.280800000000003, "compression_ratio": 5.785716262720505, "bits_per_pixel": 1.382715576902196, "compressed_bits": 1084, "mse": 0.00622074069667181, "ssim": 0.9018426821605244, "psnr": 33.62326255368204, "train_recon_loss": 6.22074069667181, "test_recon_loss": 6.842814766338991}, {"model_id": 97, "latent_dim": 15, "beta": 19.480600000000003, "compression_ratio": 5.850091011835728, "bits_per_pixel": 1.367500092530978, "compressed_bits": 1072, "mse": 0.001, "ssim": 0.9288695816127697, "psnr": 34.57384150590428, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 98, "latent_dim": 15, "beta": 19.680400000000002, "compression_ratio": 5.915182026342386, "bits_per_pixel": 1.352452040253907, "compressed_bits": 1060, "mse": 0.001, "ssim": 0.9223433343118727, "psnr": 34.37605793364033, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 99, "latent_dim": 15, "beta": 19.880200000000002, "compression_ratio": 5.980997275764522, "bits_per_pixel": 1.3375695776382708, "compressed_bits": 1048, "mse": 0.001, "ssim": 0.9028462871797359, "psnr": 32.602530384490265, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 100, "latent_dim": 16, "beta": 20.080000000000002, "compression_ratio": 6.047544818299062, "bits_per_pixel": 1.3228508825255945, "compressed_bits": 1037, "mse": 0.001, "ssim": 0.8859095224895137, "psnr": 32.21126309212706, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 101, "latent_dim": 16, "beta": 20.279800000000005, "compression_ratio": 6.114832801802424, "bits_per_pixel": 1.3082941528085443, "compressed_bits": 1025, "mse": 0.001, "ssim": 0.8862820332565499, "psnr": 34.14577212561193, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 102, "latent_dim": 16, "beta": 20.479600000000005, "compression_ratio": 6.182869464788117, "bits_per_pixel": 1.2938976062102834, "compressed_bits": 1014, "mse": 0.001, "ssim": 0.8898868075429252, "psnr": 35.78543378597058, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 103, "latent_dim": 16, "beta": 20.6794, "compression_ratio": 6.251663137435444, "bits_per_pixel": 1.2796594800662529, "compressed_bits": 1003, "mse": 0.001, "ssim": 0.906017956379497, "psnr": 34.683584458230946, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 104, "latent_dim": 16, "beta": 20.8792, "compression_ratio": 6.321222242609409, "bits_per_pixel": 1.265578031108362, "compressed_bits": 992, "mse": 0.001, "ssim": 0.9156232172086286, "psnr": 36.27715722150121, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 105, "latent_dim": 16, "beta": 21.079, "compression_ratio": 6.39155529689199, "bits_per_pixel": 1.2516515352515443, "compressed_bits": 981, "mse": 0.01122098354265395, "ssim": 0.9146489075066424, "psnr": 35.35112968285077, "train_recon_loss": 11.22098354265395, "test_recon_loss": 12.343081896919346}, {"model_id": 106, "latent_dim": 16, "beta": 21.278800000000004, "compression_ratio": 6.462670911624891, "bits_per_pixel": 1.237878287382667, "compressed_bits": 970, "mse": 0.001, "ssim": 0.8970208946821854, "psnr": 34.53486373484957, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 107, "latent_dim": 16, "beta": 21.478600000000004, "compression_ratio": 6.534577793963859, "bits_per_pixel": 1.2242566011517662, "compressed_bits": 959, "mse": 0.009684145668303232, "ssim": 0.9017953090310803, "psnr": 34.483486555826055, "train_recon_loss": 9.684145668303232, "test_recon_loss": 10.652560235133555}, {"model_id": 108, "latent_dim": 16, "beta": 21.678400000000003, "compression_ratio": 6.607284747944786, "bits_per_pixel": 1.2107848087655708, "compressed_bits": 949, "mse": 0.001, "ssim": 0.909288860131697, "psnr": 32.400140677507814, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 109, "latent_dim": 17, "beta": 21.878200000000003, "compression_ratio": 6.6808006755616365, "bits_per_pixel": 1.197461260783306, "compressed_bits": 938, "mse": 0.004141333603074223, "ssim": 0.9107925789263066, "psnr": 35.432109418307206, "train_recon_loss": 4.141333603074223, "test_recon_loss": 4.555466963381646}, {"model_id": 110, "latent_dim": 17, "beta": 22.078000000000003, "compression_ratio": 6.755134577856364, "bits_per_pixel": 1.1842843259147438, "compressed_bits": 928, "mse": 0.001, "ssim": 0.9212431531867575, "psnr": 35.2471711344115, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 111, "latent_dim": 17, "beta": 22.277800000000003, "compression_ratio": 6.830295556020991, "bits_per_pixel": 1.1712523908204675, "compressed_bits": 918, "mse": 0.004892789680770387, "ssim": 0.9164820513174858, "psnr": 36.24543119077866, "train_recon_loss": 4.892789680770387, "test_recon_loss": 5.382068648847426}, {"model_id": 112, "latent_dim": 17, "beta": 22.477600000000002, "compression_ratio": 6.906292812511915, "bits_per_pixel": 1.158363859914345, "compressed_bits": 908, "mse": 0.0016090227758354145, "ssim": 0.9049724107672907, "psnr": 32.43099489932316, "train_recon_loss": 1.6090227758354145, "test_recon_loss": 1.769925053418956}, {"model_id": 113, "latent_dim": 17, "beta": 22.677400000000002, "compression_ratio": 6.983135652176622, "bits_per_pixel": 1.1456171551681693, "compressed_bits": 898, "mse": 0.0053734622105033915, "ssim": 0.9086918145600884, "psnr": 33.3202806873691, "train_recon_loss": 5.373462210503392, "test_recon_loss": 5.910808431553731}, {"model_id": 114, "latent_dim": 17, "beta": 22.877200000000002, "compression_ratio": 7.060833483392951, "bits_per_pixel": 1.1330107159184484, "compressed_bits": 888, "mse": 0.002461224151561667, "ssim": 0.923808341857211, "psnr": 34.20099933674176, "train_recon_loss": 2.461224151561667, "test_recon_loss": 2.707346566717834}, {"model_id": 115, "latent_dim": 17, "beta": 23.077, "compression_ratio": 7.139395819221021, "bits_per_pixel": 1.1205429986753248, "compressed_bits": 878, "mse": 0.001, "ssim": 0.8958912540308451, "psnr": 34.497826690154604, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 116, "latent_dim": 17, "beta": 23.276800000000005, "compression_ratio": 7.21883227856798, "bits_per_pixel": 1.1082124769335937, "compressed_bits": 868, "mse": 0.005123008261143531, "ssim": 0.9192688089465574, "psnr": 33.361677526714956, "train_recon_loss": 5.123008261143531, "test_recon_loss": 5.635309087257885}, {"model_id": 117, "latent_dim": 18, "beta": 23.476600000000005, "compression_ratio": 7.299152587365696, "bits_per_pixel": 1.0960176409858071, "compressed_bits": 859, "mse": 0.001, "ssim": 0.9022025084263695, "psnr": 32.631096980791206, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 118, "latent_dim": 18, "beta": 23.676400000000005, "compression_ratio": 7.380366579761591, "bits_per_pixel": 1.0839569977374246, "compressed_bits": 849, "mse": 0.006305873170515162, "ssim": 0.9092464307357768, "psnr": 34.35370933071755, "train_recon_loss": 6.305873170515162, "test_recon_loss": 6.936460487566678}, {"model_id": 119, "latent_dim": 18, "beta": 23.8762, "compression_ratio": 7.462484199322689, "bits_per_pixel": 1.0720290705240083, "compressed_bits": 840, "mse": 0.001, "ssim": 0.8955969644786196, "psnr": 34.54431602030255, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 120, "latent_dim": 18, "beta": 24.076, "compression_ratio": 7.545515500253063, "bits_per_pixel": 1.0602323989304236, "compressed_bits": 831, "mse": 0.001, "ssim": 0.8802162144423819, "psnr": 33.91633107240676, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 121, "latent_dim": 18, "beta": 24.275800000000004, "compression_ratio": 7.629470648624858, "bits_per_pixel": 1.0485655386120303, "compressed_bits": 822, "mse": 0.001, "ssim": 0.9027476555164233, "psnr": 34.698573003310585, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 122, "latent_dim": 18, "beta": 24.475600000000004, "compression_ratio": 7.714359923622982, "bits_per_pixel": 1.0370270611178418, "compressed_bits": 813, "mse": 0.001, "ssim": 0.9098525013837038, "psnr": 32.955616959890634, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 123, "latent_dim": 18, "beta": 24.675400000000003, "compression_ratio": 7.800193718803639, "bits_per_pixel": 1.0256155537156335, "compressed_bits": 804, "mse": 0.0028115561331251595, "ssim": 0.940323901019917, "psnr": 32.8001772261546, "train_recon_loss": 2.8115561331251597, "test_recon_loss": 3.0927117464376757}, {"model_id": 124, "latent_dim": 18, "beta": 24.875200000000003, "compression_ratio": 7.886982543366909, "bits_per_pixel": 1.0143296192189675, "compressed_bits": 795, "mse": 0.0016950525639465863, "ssim": 0.9010394766085025, "psnr": 33.66615979545864, "train_recon_loss": 1.6950525639465863, "test_recon_loss": 1.864557820341245}, {"model_id": 125, "latent_dim": 19, "beta": 25.075000000000003, "compression_ratio": 7.974737023443446, "bits_per_pixel": 1.0031678758161289, "compressed_bits": 786, "mse": 0.001, "ssim": 0.9253726994086767, "psnr": 35.869339568028536, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 126, "latent_dim": 19, "beta": 25.274800000000003, "compression_ratio": 8.06346790339549, "bits_per_pixel": 0.9921289569009429, "compressed_bits": 777, "mse": 0.001, "ssim": 0.9120371799981238, "psnr": 34.01490743577953, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 127, "latent_dim": 19, "beta": 25.474600000000002, "compression_ratio": 8.1531860471324, "bits_per_pixel": 0.9812115109054482, "compressed_bits": 769, "mse": 0.010333877289852852, "ssim": 0.9092760915111454, "psnr": 34.67716918861833, "train_recon_loss": 10.333877289852852, "test_recon_loss": 11.367265018838136}, {"model_id": 128, "latent_dim": 19, "beta": 25.674400000000002, "compression_ratio": 8.243902439440774, "bits_per_pixel": 0.9704142011344182, "compressed_bits": 760, "mse": 0.0074495962069989164, "ssim": 0.9061139033558181, "psnr": 35.107289204958235, "train_recon_loss": 7.449596206998916, "test_recon_loss": 8.194555827698808}, {"model_id": 129, "latent_dim": 19, "beta": 25.874200000000002, "compression_ratio": 8.33562818732939, "bits_per_pixel": 0.9597357056017011, "compressed_bits": 752, "mse": 0.006920449244053271, "ssim": 0.9178910401399296, "psnr": 34.62810620996589, "train_recon_loss": 6.920449244053271, "test_recon_loss": 7.612494168458598}, {"model_id": 130, "latent_dim": 19, "beta": 26.074, "compression_ratio": 8.42837452138912, "bits_per_pixel": 0.949174716868358, "compressed_bits": 744, "mse": 0.001, "ssim": 0.921415906066697, "psnr": 32.595429836977424, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 131, "latent_dim": 19, "beta": 26.273800000000005, "compression_ratio": 8.52215279716797, "bits_per_pixel": 0.9387299418825854, "compressed_bits": 735, "mse": 0.001, "ssim": 0.9106395150265143, "psnr": 34.517309628632844, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 132, "latent_dim": 19, "beta": 26.473600000000005, "compression_ratio": 8.616974496561385, "bits_per_pixel": 0.9284001018214004, "compressed_bits": 727, "mse": 0.001, "ssim": 0.8749392265573166, "psnr": 33.128522326698246, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 133, "latent_dim": 19, "beta": 26.673400000000004, "compression_ratio": 8.712851229218089, "bits_per_pixel": 0.9181839319340631, "compressed_bits": 719, "mse": 0.001, "ssim": 0.9239270886225863, "psnr": 32.89139257712457, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 134, "latent_dim": 20, "beta": 26.8732, "compression_ratio": 8.80979473396152, "bits_per_pixel": 0.9080801813872255, "compressed_bits": 711, "mse": 0.0023608846097129934, "ssim": 0.8963712076064881, "psnr": 33.24281570002924, "train_recon_loss": 2.3608846097129934, "test_recon_loss": 2.596973070684293}, {"model_id": 135, "latent_dim": 20, "beta": 27.073, "compression_ratio": 8.907816880227072, "bits_per_pixel": 0.8980876131117852, "compressed_bits": 704, "mse": 0.005810282374961044, "ssim": 0.8653832785096333, "psnr": 34.770385675576186, "train_recon_loss": 5.8102823749610435, "test_recon_loss": 6.391310612457148}, {"model_id": 136, "latent_dim": 20, "beta": 27.272800000000004, "compression_ratio": 9.00692966951538, "bits_per_pixel": 0.888205003651421, "compressed_bits": 696, "mse": 0.001, "ssim": 0.9111693490832801, "psnr": 34.105616429004826, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 137, "latent_dim": 20, "beta": 27.472600000000003, "compression_ratio": 9.107145236861728, "bits_per_pixel": 0.8784311430127973, "compressed_bits": 688, "mse": 0.001, "ssim": 0.9561083469010981, "psnr": 35.61121218741286, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 138, "latent_dim": 20, "beta": 27.672400000000003, "compression_ratio": 9.208475852321808, "bits_per_pixel": 0.8687648345174186, "compressed_bits": 681, "mse": 0.0037934933654735804, "ssim": 0.9183903429210892, "psnr": 34.3697585596006, "train_recon_loss": 3.7934933654735805, "test_recon_loss": 4.172842702020938}, {"model_id": 139, "latent_dim": 20, "beta": 27.872200000000003, "compression_ratio": 9.310933922474051, "bits_per_pixel": 0.8592048946551092, "compressed_bits": 673, "mse": 0.001, "ssim": 0.9183304481646342, "psnr": 34.29991297789276, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 140, "latent_dim": 20, "beta": 28.072000000000003, "compression_ratio": 9.414531991938636, "bits_per_pixel": 0.8497501529391099, "compressed_bits": 666, "mse": 0.0010957946424970926, "ssim": 0.8995144964843258, "psnr": 34.363896883648074, "train_recon_loss": 1.0957946424970926, "test_recon_loss": 1.2053741067468018}, {"model_id": 141, "latent_dim": 20, "beta": 28.271800000000006, "compression_ratio": 9.519282744913394, "bits_per_pixel": 0.8403994517627686, "compressed_bits": 658, "mse": 0.003217972575620596, "ssim": 0.9042376659762508, "psnr": 34.59667899312137, "train_recon_loss": 3.2179725756205957, "test_recon_loss": 3.5397698331826555}, {"model_id": 142, "latent_dim": 21, "beta": 28.471600000000002, "compression_ratio": 9.625199006726847, "bits_per_pixel": 0.8311516462578041, "compressed_bits": 651, "mse": 0.001, "ssim": 0.9062840324580256, "psnr": 36.0578691959277, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 143, "latent_dim": 21, "beta": 28.671400000000002, "compression_ratio": 9.732293745408484, "bits_per_pixel": 0.8220056041541339, "compressed_bits": 644, "mse": 0.004435552342748584, "ssim": 0.8745293967484277, "psnr": 35.69860215176543, "train_recon_loss": 4.435552342748584, "test_recon_loss": 4.879107577023442}, {"model_id": 144, "latent_dim": 21, "beta": 28.8712, "compression_ratio": 9.84058007327651, "bits_per_pixel": 0.812960205641244, "compressed_bits": 637, "mse": 0.0047386460787257586, "ssim": 0.9217127479665135, "psnr": 33.39311533023417, "train_recon_loss": 4.7386460787257585, "test_recon_loss": 5.212510686598335}, {"model_id": 145, "latent_dim": 21, "beta": 29.071, "compression_ratio": 9.950071248543294, "bits_per_pixel": 0.8040143432310811, "compressed_bits": 630, "mse": 0.013824581530023299, "ssim": 0.9061977074396392, "psnr": 34.92225778278539, "train_recon_loss": 13.824581530023298, "test_recon_loss": 15.207039683025629}, {"model_id": 146, "latent_dim": 21, "beta": 29.270800000000005, "compression_ratio": 10.060780676938665, "bits_per_pixel": 0.7951669216224553, "compressed_bits": 623, "mse": 0.001, "ssim": 0.8914572589239096, "psnr": 34.77080682248506, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 147, "latent_dim": 21, "beta": 29.470600000000005, "compression_ratio": 10.172721913351227, "bits_per_pixel": 0.7864168575669379, "compressed_bits": 616, "mse": 0.001, "ssim": 0.8978725994813695, "psnr": 34.59357872702588, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 148, "latent_dim": 21, "beta": 29.670400000000004, "compression_ratio": 10.285908663488016, "bits_per_pixel": 0.7777630797362292, "compressed_bits": 609, "mse": 0.003223470900638247, "ssim": 0.9269861879345292, "psnr": 31.927794399101757, "train_recon_loss": 3.223470900638247, "test_recon_loss": 3.545817990702072}, {"model_id": 149, "latent_dim": 21, "beta": 29.8702, "compression_ratio": 10.400354785552564, "bits_per_pixel": 0.7692045285909894, "compressed_bits": 603, "mse": 0.001, "ssim": 0.8587340401811502, "psnr": 33.104657333544914, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 150, "latent_dim": 22, "beta": 30.07, "compression_ratio": 10.516074291941635, "bits_per_pixel": 0.7607401562511138, "compressed_bits": 596, "mse": 0.001, "ssim": 0.9073606952162397, "psnr": 32.342980310855225, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 151, "latent_dim": 22, "beta": 30.269800000000004, "compression_ratio": 10.633081350960886, "bits_per_pixel": 0.7523689263674315, "compressed_bits": 589, "mse": 0.001, "ssim": 0.9144697311070985, "psnr": 33.87160724986891, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 152, "latent_dim": 22, "beta": 30.469600000000003, "compression_ratio": 10.751390288559568, "bits_per_pixel": 0.7440898139948197, "compressed_bits": 583, "mse": 0.0036433690701818703, "ssim": 0.899036240559996, "psnr": 33.505351831694604, "train_recon_loss": 3.6433690701818704, "test_recon_loss": 4.007705977200057}, {"model_id": 153, "latent_dim": 22, "beta": 30.669400000000003, "compression_ratio": 10.871015590084536, "bits_per_pixel": 0.7359018054667136, "compressed_bits": 576, "mse": 0.00468393009009475, "ssim": 0.9029024730233878, "psnr": 34.430747260323436, "train_recon_loss": 4.68393009009475, "test_recon_loss": 5.152323099104224}, {"model_id": 154, "latent_dim": 22, "beta": 30.869200000000003, "compression_ratio": 10.991971902053816, "bits_per_pixel": 0.727803898270994, "compressed_bits": 570, "mse": 0.001, "ssim": 0.9289134440653433, "psnr": 34.138858636425866, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 155, "latent_dim": 22, "beta": 31.069000000000003, "compression_ratio": 11.114274033949856, "bits_per_pixel": 0.7197951009272454, "compressed_bits": 564, "mse": 0.011690662589882545, "ssim": 0.882921361014829, "psnr": 33.822950968122285, "train_recon_loss": 11.690662589882544, "test_recon_loss": 12.8597288488708}, {"model_id": 156, "latent_dim": 22, "beta": 31.268800000000006, "compression_ratio": 11.23793696003274, "bits_per_pixel": 0.7118744328653622, "compressed_bits": 558, "mse": 0.001, "ssim": 0.9032863293248823, "psnr": 33.782346971882134, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 157, "latent_dim": 22, "beta": 31.468600000000002, "compression_ratio": 11.362975821173611, "bits_per_pixel": 0.7040409243054897, "compressed_bits": 551, "mse": 0.0013425465050656389, "ssim": 0.8753280728085833, "psnr": 34.351276601964585, "train_recon_loss": 1.3425465050656389, "test_recon_loss": 1.4768011555722027}, {"model_id": 158, "latent_dim": 22, "beta": 31.668400000000002, "compression_ratio": 11.489405926708455, "bits_per_pixel": 0.6962936161392882, "compressed_bits": 545, "mse": 0.001, "ssim": 0.8951161827950028, "psnr": 35.132523660875265, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 159, "latent_dim": 23, "beta": 31.8682, "compression_ratio": 11.617242756312507, "bits_per_pixel": 0.6886315598125045, "compressed_bits": 539, "mse": 0.007963789071073606, "ssim": 0.9054589713822729, "psnr": 33.15768159338539, "train_recon_loss": 7.963789071073606, "test_recon_loss": 8.760167978180966}, {"model_id": 160, "latent_dim": 23, "beta": 32.068, "compression_ratio": 11.746501961895568, "bits_per_pixel": 0.6810538172088311, "compressed_bits": 533, "mse": 0.001, "ssim": 0.9074768833092237, "psnr": 34.03054616014207, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 161, "latent_dim": 23, "beta": 32.2678, "compression_ratio": 11.877199369518346, "bits_per_pixel": 0.6735594605350489, "compressed_bits": 528, "mse": 0.001, "ssim": 0.8909119295197687, "psnr": 34.312058759476294, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 162, "latent_dim": 23, "beta": 32.467600000000004, "compression_ratio": 12.009350981330138, "bits_per_pixel": 0.6661475722074309, "compressed_bits": 522, "mse": 0.007728437626587379, "ssim": 0.8938472649546719, "psnr": 33.91913173526, "train_recon_loss": 7.728437626587379, "test_recon_loss": 8.501281389246117}, {"model_id": 163, "latent_dim": 23, "beta": 32.6674, "compression_ratio": 12.142972977528107, "bits_per_pixel": 0.6588172447393954, "compressed_bits": 516, "mse": 0.001, "ssim": 0.9227433641972317, "psnr": 33.45111023671256, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 164, "latent_dim": 23, "beta": 32.867200000000004, "compression_ratio": 12.278081718338317, "bits_per_pixel": 0.6515675806303967, "compressed_bits": 510, "mse": 0.001, "ssim": 0.9077897434674574, "psnr": 33.19763400864378, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 165, "latent_dim": 23, "beta": 33.067, "compression_ratio": 12.414693746018806, "bits_per_pixel": 0.6443976922560392, "compressed_bits": 505, "mse": 0.001, "ssim": 0.9229338953630107, "psnr": 33.62512443631867, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 166, "latent_dim": 23, "beta": 33.2668, "compression_ratio": 12.552825786884991, "bits_per_pixel": 0.6373067017593985, "compressed_bits": 499, "mse": 0.001, "ssim": 0.8885989334678389, "psnr": 32.514946294356, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 167, "latent_dim": 24, "beta": 33.4666, "compression_ratio": 12.692494753357584, "bits_per_pixel": 0.6302937409435395, "compressed_bits": 494, "mse": 0.001, "ssim": 0.8952083109011862, "psnr": 32.469305736780676, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 168, "latent_dim": 24, "beta": 33.6664, "compression_ratio": 12.833717746033251, "bits_per_pixel": 0.623357951165219, "compressed_bits": 488, "mse": 0.006293401819961411, "ssim": 0.9208841150223046, "psnr": 32.8624929761861, "train_recon_loss": 6.293401819961411, "test_recon_loss": 6.922742001957552}, {"model_id": 169, "latent_dim": 24, "beta": 33.866200000000006, "compression_ratio": 12.9765120557784, "bits_per_pixel": 0.616498483229754, "compressed_bits": 483, "mse": 0.001, "ssim": 0.8933970925868038, "psnr": 35.614372463075206, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 170, "latent_dim": 24, "beta": 34.066, "compression_ratio": 13.120895165846198, "bits_per_pixel": 0.6097144972870501, "compressed_bits": 478, "mse": 0.001, "ssim": 0.8888721289501706, "psnr": 33.264523756108474, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 171, "latent_dim": 24, "beta": 34.265800000000006, "compression_ratio": 13.266884754017138, "bits_per_pixel": 0.6030051627287744, "compressed_bits": 472, "mse": 0.0010352301166866736, "ssim": 0.8909100626395662, "psnr": 33.00534620244301, "train_recon_loss": 1.0352301166866735, "test_recon_loss": 1.138753128355341}, {"model_id": 172, "latent_dim": 24, "beta": 34.4656, "compression_ratio": 13.414498694763486, "bits_per_pixel": 0.5963696580866565, "compressed_bits": 467, "mse": 0.006574489396217821, "ssim": 0.8582947567073235, "psnr": 33.20283469605971, "train_recon_loss": 6.574489396217821, "test_recon_loss": 7.231938335839603}, {"model_id": 173, "latent_dim": 24, "beta": 34.665400000000005, "compression_ratio": 13.563755061437758, "bits_per_pixel": 0.5898071709319115, "compressed_bits": 462, "mse": 0.00444979597637556, "ssim": 0.8665249909658229, "psnr": 34.63867902703501, "train_recon_loss": 4.44979597637556, "test_recon_loss": 4.894775574013115}, {"model_id": 174, "latent_dim": 24, "beta": 34.86520000000001, "compression_ratio": 13.71467212848553, "bits_per_pixel": 0.5833168977757703, "compressed_bits": 457, "mse": 0.001, "ssim": 0.9098358071507077, "psnr": 33.31637601259214, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 175, "latent_dim": 25, "beta": 35.065000000000005, "compression_ratio": 13.867268373682947, "bits_per_pixel": 0.5768980439711008, "compressed_bits": 452, "mse": 0.0053964359359173365, "ssim": 0.8709639587883805, "psnr": 33.042052252725746, "train_recon_loss": 5.396435935917337, "test_recon_loss": 5.93607952950907}, {"model_id": 176, "latent_dim": 25, "beta": 35.2648, "compression_ratio": 14.021562480399055, "bits_per_pixel": 0.5705498236151153, "compressed_bits": 447, "mse": 0.001, "ssim": 0.8938059773498648, "psnr": 32.97572787317106, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 177, "latent_dim": 25, "beta": 35.4646, "compression_ratio": 14.177573339883317, "bits_per_pixel": 0.5642714594531479, "compressed_bits": 442, "mse": 0.001, "ssim": 0.8966800490246225, "psnr": 33.435500947701264, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 178, "latent_dim": 25, "beta": 35.6644, "compression_ratio": 14.335320053578622, "bits_per_pixel": 0.5580621827834884, "compressed_bits": 437, "mse": 0.001, "ssim": 0.8838025346230339, "psnr": 33.93306550106235, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 179, "latent_dim": 25, "beta": 35.864200000000004, "compression_ratio": 14.494821935459987, "bits_per_pixel": 0.5519212333632662, "compressed_bits": 432, "mse": 0.003612925803452575, "ssim": 0.9183035688151509, "psnr": 32.88999761593828, "train_recon_loss": 3.6129258034525753, "test_recon_loss": 3.9742183837978327}, {"model_id": 180, "latent_dim": 25, "beta": 36.064, "compression_ratio": 14.656098514399295, "bits_per_pixel": 0.545847859315368, "compressed_bits": 427, "mse": 0.01048137493889697, "ssim": 0.8707021346868312, "psnr": 33.83574646577134, "train_recon_loss": 10.48137493889697, "test_recon_loss": 11.529512432786667}, {"model_id": 181, "latent_dim": 25, "beta": 36.2638, "compression_ratio": 14.819169536556332, "bits_per_pixel": 0.5398413170363819, "compressed_bits": 423, "mse": 0.0018927604654164836, "ssim": 0.9027581410743988, "psnr": 33.12154415884109, "train_recon_loss": 1.8927604654164836, "test_recon_loss": 2.082036511958132}, {"model_id": 182, "latent_dim": 25, "beta": 36.4636, "compression_ratio": 14.984054967796471, "bits_per_pixel": 0.5339008711055514, "compressed_bits": 418, "mse": 0.0026866601867029474, "ssim": 0.9379401618678882, "psnr": 33.53147107326896, "train_recon_loss": 2.6866601867029476, "test_recon_loss": 2.9553262053732423}, {"model_id": 183, "latent_dim": 25, "beta": 36.6634, "compression_ratio": 15.150774996135205, "bits_per_pixel": 0.5280257941947334, "compressed_bits": 413, "mse": 0.001, "ssim": 0.9201795306364429, "psnr": 33.34482255211517, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 184, "latent_dim": 26, "beta": 36.863200000000006, "compression_ratio": 15.31935003420988, "bits_per_pixel": 0.522215366979348, "compressed_bits": 409, "mse": 0.004256424982466436, "ssim": 0.9250782377269212, "psnr": 32.23726124816325, "train_recon_loss": 4.256424982466436, "test_recon_loss": 4.68206748071308}, {"model_id": 185, "latent_dim": 26, "beta": 37.063, "compression_ratio": 15.489800721778995, "bits_per_pixel": 0.5164688780503048, "compressed_bits": 404, "mse": 0.001, "ssim": 0.9148102884580849, "psnr": 34.58650303062621, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 186, "latent_dim": 26, "beta": 37.262800000000006, "compression_ratio": 15.66214792824924, "bits_per_pixel": 0.5107856238269014, "compressed_bits": 400, "mse": 0.0025690288628279663, "ssim": 0.9010554976285795, "psnr": 33.81732264766439, "train_recon_loss": 2.5690288628279663, "test_recon_loss": 2.825931749110763}, {"model_id": 187, "latent_dim": 26, "beta": 37.4626, "compression_ratio": 15.836412755230652, "bits_per_pixel": 0.5051649084706799, "compressed_bits": 396, "mse": 0.001, "ssim": 0.8747960431749889, "psnr": 33.33288232629469, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 188, "latent_dim": 26, "beta": 37.662400000000005, "compression_ratio": 16.012616539120273, "bits_per_pixel": 0.4996060438002293, "compressed_bits": 391, "mse": 0.00350352990573446, "ssim": 0.8976525905490718, "psnr": 33.05860585279292, "train_recon_loss": 3.50352990573446, "test_recon_loss": 3.853882896307906}, {"model_id": 189, "latent_dim": 26, "beta": 37.86220000000001, "compression_ratio": 16.190780853714482, "bits_per_pixel": 0.49410834920692803, "compressed_bits": 387, "mse": 0.001, "ssim": 0.9081414563016801, "psnr": 33.350783556991146, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 190, "latent_dim": 26, "beta": 38.062000000000005, "compression_ratio": 16.370927512850393, "bits_per_pixel": 0.4886711515716128, "compressed_bits": 383, "mse": 0.0029167181580979024, "ssim": 0.9003913908242637, "psnr": 32.19825844729237, "train_recon_loss": 2.9167181580979022, "test_recon_loss": 3.2083899739076926}, {"model_id": 191, "latent_dim": 26, "beta": 38.2618, "compression_ratio": 16.553078573076718, "bits_per_pixel": 0.4832937851821627, "compressed_bits": 378, "mse": 0.0026294684529862404, "ssim": 0.9150951811849811, "psnr": 33.42247559199815, "train_recon_loss": 2.62946845298624, "test_recon_loss": 2.8924152982848645}, {"model_id": 192, "latent_dim": 27, "beta": 38.461600000000004, "compression_ratio": 16.737256336354267, "bits_per_pixel": 0.477975591651993, "compressed_bits": 374, "mse": 0.001, "ssim": 0.88789076932922, "psnr": 33.22030927685031, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 193, "latent_dim": 27, "beta": 38.6614, "compression_ratio": 16.923483352786512, "bits_per_pixel": 0.4727159198394444, "compressed_bits": 370, "mse": 0.0020458151428393682, "ssim": 0.8859173546372068, "psnr": 33.99349987866951, "train_recon_loss": 2.045815142839368, "test_recon_loss": 2.250396657123305}, {"model_id": 194, "latent_dim": 27, "beta": 38.861200000000004, "compression_ratio": 17.111782423380582, "bits_per_pixel": 0.46751412576805834, "compressed_bits": 366, "mse": 0.01066565559500776, "ssim": 0.8924994654721772, "psnr": 34.16925308269043, "train_recon_loss": 10.66565559500776, "test_recon_loss": 11.732221154508535}, {"model_id": 195, "latent_dim": 27, "beta": 39.061, "compression_ratio": 17.30217660283892, "bits_per_pixel": 0.4623695725477319, "compressed_bits": 362, "mse": 0.001, "ssim": 0.8660743786289534, "psnr": 32.77580094207047, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 196, "latent_dim": 27, "beta": 39.2608, "compression_ratio": 17.494689202382002, "bits_per_pixel": 0.4572816302967391, "compressed_bits": 358, "mse": 0.004313680260656509, "ssim": 0.9020647446709541, "psnr": 33.05635332999058, "train_recon_loss": 4.313680260656509, "test_recon_loss": 4.7450482867221595}, {"model_id": 197, "latent_dim": 27, "beta": 39.46060000000001, "compression_ratio": 17.689343792602532, "bits_per_pixel": 0.45224967606460914, "compressed_bits": 354, "mse": 0.001, "ssim": 0.908015345350492, "psnr": 34.327057051604854, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 198, "latent_dim": 27, "beta": 39.6604, "compression_ratio": 17.88616420635132, "bits_per_pixel": 0.44727309375585544, "compressed_bits": 350, "mse": 0.001, "ssim": 0.9088168820733402, "psnr": 33.79491286547687, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 199, "latent_dim": 27, "beta": 39.860200000000006, "compression_ratio": 18.08517454165527, "bits_per_pixel": 0.4423512740545433, "compressed_bits": 346, "mse": 0.001, "ssim": 0.9355712758250979, "psnr": 32.344648251427465, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 200, "latent_dim": 28, "beta": 40.06, "compression_ratio": 18.286399164667916, "bits_per_pixel": 0.4374836143496861, "compressed_bits": 342, "mse": 0.001, "ssim": 0.9269364047850097, "psnr": 31.775256330303005, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 201, "latent_dim": 28, "beta": 40.259800000000006, "compression_ratio": 18.489862712652695, "bits_per_pixel": 0.4326695186614644, "compressed_bits": 339, "mse": 0.003920438221122502, "ssim": 0.8926123190901802, "psnr": 33.78566765239829, "train_recon_loss": 3.9204382211225024, "test_recon_loss": 4.312482043234753}, {"model_id": 202, "latent_dim": 28, "beta": 40.45960000000001, "compression_ratio": 18.69559009699944, "bits_per_pixel": 0.42790839756825677, "compressed_bits": 335, "mse": 0.0012618368992095756, "ssim": 0.8748907971714499, "psnr": 33.307609311888015, "train_recon_loss": 1.2618368992095756, "test_recon_loss": 1.3880205891305333}, {"model_id": 203, "latent_dim": 28, "beta": 40.659400000000005, "compression_ratio": 18.903606506274485, "bits_per_pixel": 0.4231996681344716, "compressed_bits": 331, "mse": 0.0041549658388132585, "ssim": 0.8997132155518506, "psnr": 34.424245038252174, "train_recon_loss": 4.154965838813259, "test_recon_loss": 4.570462422694584}, {"model_id": 204, "latent_dim": 28, "beta": 40.85920000000001, "compression_ratio": 19.113937409304654, "bits_per_pixel": 0.4185427538391752, "compressed_bits": 328, "mse": 0.004127478301974368, "ssim": 0.9121650433826056, "psnr": 35.1169100670857, "train_recon_loss": 4.127478301974368, "test_recon_loss": 4.540226132171805}, {"model_id": 205, "latent_dim": 28, "beta": 41.059000000000005, "compression_ratio": 19.326608558295543, "bits_per_pixel": 0.4139370845055051, "compressed_bits": 324, "mse": 0.0016380221075692181, "ssim": 0.8805187275776685, "psnr": 33.246248075329305, "train_recon_loss": 1.638022107569218, "test_recon_loss": 1.80182431832614}, {"model_id": 206, "latent_dim": 28, "beta": 41.2588, "compression_ratio": 19.541645991984584, "bits_per_pixel": 0.4093820962308583, "compressed_bits": 320, "mse": 0.005250069368323701, "ssim": 0.8923145482867215, "psnr": 35.20876295706044, "train_recon_loss": 5.250069368323701, "test_recon_loss": 5.775076305156071}, {"model_id": 207, "latent_dim": 28, "beta": 41.458600000000004, "compression_ratio": 19.759076038829125, "bits_per_pixel": 0.40487723131784964, "compressed_bits": 317, "mse": 0.001, "ssim": 0.8923931532024298, "psnr": 33.247159656667314, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 208, "latent_dim": 28, "beta": 41.6584, "compression_ratio": 19.97892532022996, "bits_per_pixel": 0.40042193820602956, "compressed_bits": 313, "mse": 0.001, "ssim": 0.9241254300706799, "psnr": 33.35256500877624, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 209, "latent_dim": 29, "beta": 41.858200000000004, "compression_ratio": 20.201220753790825, "bits_per_pixel": 0.39601567140435184, "compressed_bits": 310, "mse": 0.0025203280640570995, "ssim": 0.8999656511956338, "psnr": 33.03907990954364, "train_recon_loss": 2.5203280640570997, "test_recon_loss": 2.7723608704628093}, {"model_id": 210, "latent_dim": 29, "beta": 42.058, "compression_ratio": 20.42598955661404, "bits_per_pixel": 0.39165789142438673, "compressed_bits": 307, "mse": 0.0017141614873531713, "ssim": 0.8865251835271818, "psnr": 32.878460157972704, "train_recon_loss": 1.7141614873531714, "test_recon_loss": 1.8855776360884884}, {"model_id": 211, "latent_dim": 29, "beta": 42.2578, "compression_ratio": 20.653259248632928, "bits_per_pixel": 0.38734806471426697, "compressed_bits": 303, "mse": 0.0016795918655579689, "ssim": 0.8983820448398057, "psnr": 31.505643700756508, "train_recon_loss": 1.679591865557969, "test_recon_loss": 1.8475510521137657}, {"model_id": 212, "latent_dim": 29, "beta": 42.457600000000006, "compression_ratio": 20.883057655981247, "bits_per_pixel": 0.38308566359336127, "compressed_bits": 300, "mse": 0.001, "ssim": 0.901261863822103, "psnr": 34.3512189900602, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 213, "latent_dim": 29, "beta": 42.6574, "compression_ratio": 21.115412914400085, "bits_per_pixel": 0.3788701661876684, "compressed_bits": 297, "mse": 0.00636855052379657, "ssim": 0.8832565671079767, "psnr": 34.70967642239227, "train_recon_loss": 6.3685505237965705, "test_recon_loss": 7.005405576176227}, {"model_id": 214, "latent_dim": 29, "beta": 42.857200000000006, "compression_ratio": 21.35035347268278, "bits_per_pixel": 0.37470105636591877, "compressed_bits": 293, "mse": 0.001, "ssim": 0.8855620630996683, "psnr": 34.02071633037195, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 215, "latent_dim": 29, "beta": 43.057, "compression_ratio": 21.587908096158053, "bits_per_pixel": 0.3705778236763821, "compressed_bits": 290, "mse": 0.012871257972282172, "ssim": 0.8856259869264581, "psnr": 33.9352936641523, "train_recon_loss": 12.871257972282171, "test_recon_loss": 14.158383769510388}, {"model_id": 216, "latent_dim": 29, "beta": 43.256800000000005, "compression_ratio": 21.828105870211903, "bits_per_pixel": 0.36649996328436985, "compressed_bits": 287, "mse": 0.005500894045985505, "ssim": 0.8553998398267697, "psnr": 31.785894222241808, "train_recon_loss": 5.500894045985506, "test_recon_loss": 6.050983450584056}, {"model_id": 217, "latent_dim": 30, "beta": 43.45660000000001, "compression_ratio": 22.070976203848808, "bits_per_pixel": 0.36246697591042365, "compressed_bits": 284, "mse": 0.005158801799898831, "ssim": 0.8964322137825104, "psnr": 34.10418268075164, "train_recon_loss": 5.158801799898831, "test_recon_loss": 5.674681979888715}, {"model_id": 218, "latent_dim": 30, "beta": 43.656400000000005, "compression_ratio": 22.316548833292405, "bits_per_pixel": 0.3584783677691863, "compressed_bits": 281, "mse": 0.00904839252259109, "ssim": 0.8827661853831157, "psnr": 33.54653214314182, "train_recon_loss": 9.04839252259109, "test_recon_loss": 9.9532317748502}, {"model_id": 219, "latent_dim": 30, "beta": 43.85620000000001, "compression_ratio": 22.56485382562628, "bits_per_pixel": 0.3545336505089442, "compressed_bits": 277, "mse": 0.0068573304245589, "ssim": 0.8768222059381279, "psnr": 32.264082587625964, "train_recon_loss": 6.8573304245589, "test_recon_loss": 7.5430634670147905}, {"model_id": 220, "latent_dim": 30, "beta": 44.056000000000004, "compression_ratio": 22.815921582475337, "bits_per_pixel": 0.35063234115183467, "compressed_bits": 274, "mse": 0.007522721313544807, "ssim": 0.871667703880577, "psnr": 34.02425137922119, "train_recon_loss": 7.522721313544807, "test_recon_loss": 8.274993444899287}, {"model_id": 221, "latent_dim": 30, "beta": 44.25580000000001, "compression_ratio": 23.069782843728035, "bits_per_pixel": 0.34677396203471217, "compressed_bits": 271, "mse": 0.0023585107439254363, "ssim": 0.8764610098984649, "psnr": 32.639928147251744, "train_recon_loss": 2.3585107439254362, "test_recon_loss": 2.5943618183179797}, {"model_id": 222, "latent_dim": 30, "beta": 44.455600000000004, "compression_ratio": 23.326468691300057, "bits_per_pixel": 0.34295804075066516, "compressed_bits": 268, "mse": 0.0021394689899498754, "ssim": 0.8693410221135043, "psnr": 33.603172184115955, "train_recon_loss": 2.1394689899498753, "test_recon_loss": 2.353415888944863}, {"model_id": 223, "latent_dim": 30, "beta": 44.6554, "compression_ratio": 23.58601055293992, "bits_per_pixel": 0.33918411009117544, "compressed_bits": 265, "mse": 0.0065101244520553055, "ssim": 0.8777845986777731, "psnr": 33.396371950207424, "train_recon_loss": 6.510124452055305, "test_recon_loss": 7.161136897260836}, {"model_id": 224, "latent_dim": 30, "beta": 44.8552, "compression_ratio": 23.84844020607687, "bits_per_pixel": 0.3354517079889151, "compressed_bits": 262, "mse": 0.00984489849213676, "ssim": 0.8729059414918641, "psnr": 31.999223870476825, "train_recon_loss": 9.84489849213676, "test_recon_loss": 10.829388341350436}, {"model_id": 225, "latent_dim": 31, "beta": 45.055, "compression_ratio": 24.113789781711553, "bits_per_pixel": 0.33176037746117293, "compressed_bits": 260, "mse": 0.0012791344404516072, "ssim": 0.894128862682172, "psnr": 35.350793863550315, "train_recon_loss": 1.2791344404516072, "test_recon_loss": 1.407047884496768}, {"model_id": 226, "latent_dim": 31, "beta": 45.2548, "compression_ratio": 24.3820917683501, "bits_per_pixel": 0.3281096665539024, "compressed_bits": 257, "mse": 0.001, "ssim": 0.8320677210928852, "psnr": 34.19870842965851, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 227, "latent_dim": 31, "beta": 45.454600000000006, "compression_ratio": 24.653379015981876, "bits_per_pixel": 0.324499128286386, "compressed_bits": 254, "mse": 0.007277383492140729, "ssim": 0.883192437937336, "psnr": 32.76970858588689, "train_recon_loss": 7.277383492140729, "test_recon_loss": 8.005121841354802}, {"model_id": 228, "latent_dim": 31, "beta": 45.6544, "compression_ratio": 24.927684740101526, "bits_per_pixel": 0.32092832059650866, "compressed_bits": 251, "mse": 0.001, "ssim": 0.897995813462544, "psnr": 31.72332433111964, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 229, "latent_dim": 31, "beta": 45.854200000000006, "compression_ratio": 25.205042525775728, "bits_per_pixel": 0.3173968062866336, "compressed_bits": 248, "mse": 0.0028437799063424658, "ssim": 0.8758187530094508, "psnr": 33.1700188787707, "train_recon_loss": 2.8437799063424656, "test_recon_loss": 3.1281578969767123}, {"model_id": 230, "latent_dim": 31, "beta": 46.054, "compression_ratio": 25.48548633175531, "bits_per_pixel": 0.31390415297007207, "compressed_bits": 246, "mse": 0.001, "ssim": 0.9020394316679273, "psnr": 31.78032805011606, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 231, "latent_dim": 31, "beta": 46.253800000000005, "compression_ratio": 25.76905049463301, "bits_per_pixel": 0.31044993301814444, "compressed_bits": 243, "mse": 0.008085533450856698, "ssim": 0.8476063252068001, "psnr": 32.57962256125798, "train_recon_loss": 8.085533450856698, "test_recon_loss": 8.894086795942368}, {"model_id": 232, "latent_dim": 31, "beta": 46.45360000000001, "compression_ratio": 26.055769733047487, "bits_per_pixel": 0.3070337235078228, "compressed_bits": 240, "mse": 0.002684780796725906, "ssim": 0.8728160369680052, "psnr": 32.89394689582227, "train_recon_loss": 2.684780796725906, "test_recon_loss": 2.953258876398497}, {"model_id": 233, "latent_dim": 31, "beta": 46.653400000000005, "compression_ratio": 26.345679151934235, "bits_per_pixel": 0.30365510616994895, "compressed_bits": 238, "mse": 0.008462995929002825, "ssim": 0.8916940393850318, "psnr": 32.222772046659934, "train_recon_loss": 8.462995929002826, "test_recon_loss": 9.309295521903108}, {"model_id": 234, "latent_dim": 32, "beta": 46.85320000000001, "compression_ratio": 26.638814246823678, "bits_per_pixel": 0.3003136673380232, "compressed_bits": 235, "mse": 0.001, "ssim": 0.8448752038078777, "psnr": 32.642671241758435, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 235, "latent_dim": 32, "beta": 47.053000000000004, "compression_ratio": 26.935210908187077, "bits_per_pixel": 0.29700899789755736, "compressed_bits": 232, "mse": 0.005652920547726077, "ssim": 0.8714799766614065, "psnr": 32.51313690503525, "train_recon_loss": 5.652920547726077, "test_recon_loss": 6.218212602498684}, {"model_id": 236, "latent_dim": 32, "beta": 47.25280000000001, "compression_ratio": 27.234905425830913, "bits_per_pixel": 0.29374069323598273, "compressed_bits": 230, "mse": 0.001, "ssim": 0.8668019100602973, "psnr": 35.54468570688502, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 237, "latent_dim": 32, "beta": 47.452600000000004, "compression_ratio": 27.537934493340067, "bits_per_pixel": 0.29050835319311136, "compressed_bits": 227, "mse": 0.001, "ssim": 0.9257182636027976, "psnr": 34.13482237653177, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 238, "latent_dim": 32, "beta": 47.6524, "compression_ratio": 27.844335212570396, "bits_per_pixel": 0.28731158201214224, "compressed_bits": 225, "mse": 0.008505070980118958, "ssim": 0.8803906588813849, "psnr": 33.32784557351059, "train_recon_loss": 8.505070980118958, "test_recon_loss": 9.355578078130854}, {"model_id": 239, "latent_dim": 32, "beta": 47.8522, "compression_ratio": 28.154145098191456, "bits_per_pixel": 0.2841499882912054, "compressed_bits": 222, "mse": 0.00738028781610655, "ssim": 0.8504383115256932, "psnr": 32.32030068616304, "train_recon_loss": 7.3802878161065495, "test_recon_loss": 8.118316597717204}, {"model_id": 240, "latent_dim": 32, "beta": 48.052, "compression_ratio": 28.467402082279627, "bits_per_pixel": 0.2810231849354401, "compressed_bits": 220, "mse": 0.007422341522701751, "ssim": 0.9055274116456229, "psnr": 31.348787030288925, "train_recon_loss": 7.422341522701751, "test_recon_loss": 8.164575674971926}, {"model_id": 241, "latent_dim": 32, "beta": 48.2518, "compression_ratio": 28.78414451896234, "bits_per_pixel": 0.277930789109601, "compressed_bits": 217, "mse": 0.0016118845565966562, "ssim": 0.8527221175539954, "psnr": 33.22922216838656, "train_recon_loss": 1.6118845565966562, "test_recon_loss": 1.7730730122563219}, {"model_id": 242, "latent_dim": 33, "beta": 48.451600000000006, "compression_ratio": 29.104411189114117, "bits_per_pixel": 0.2748724221911842, "compressed_bits": 215, "mse": 0.001, "ssim": 0.8964089163522148, "psnr": 35.02656150940445, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 243, "latent_dim": 33, "beta": 48.6514, "compression_ratio": 29.42824130510473, "bits_per_pixel": 0.2718477097240701, "compressed_bits": 213, "mse": 0.004418338994656808, "ssim": 0.8612261276079533, "psnr": 31.08361271269634, "train_recon_loss": 4.418338994656808, "test_recon_loss": 4.860172894122489}, {"model_id": 244, "latent_dim": 33, "beta": 48.851200000000006, "compression_ratio": 29.75567451560018, "bits_per_pixel": 0.26885628137267714, "compressed_bits": 210, "mse": 0.001, "ssim": 0.8852734678703067, "psnr": 35.34304250534111, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 245, "latent_dim": 33, "beta": 49.051, "compression_ratio": 30.086750910417244, "bits_per_pixel": 0.2658977708766179, "compressed_bits": 208, "mse": 0.001, "ssim": 0.8723074374039855, "psnr": 33.12803951572418, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 246, "latent_dim": 33, "beta": 49.250800000000005, "compression_ratio": 30.42151102543188, "bits_per_pixel": 0.2629718160058563, "compressed_bits": 206, "mse": 0.001, "ssim": 0.8747991039250319, "psnr": 32.49115326072684, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 247, "latent_dim": 33, "beta": 49.45060000000001, "compression_ratio": 30.759995847542267, "bits_per_pixel": 0.2600780585163571, "compressed_bits": 203, "mse": 0.008428614871095362, "ssim": 0.8793250712018601, "psnr": 33.29227662748463, "train_recon_loss": 8.428614871095363, "test_recon_loss": 9.271476358204898}, {"model_id": 248, "latent_dim": 33, "beta": 49.650400000000005, "compression_ratio": 31.102246819687192, "bits_per_pixel": 0.25721614410622373, "compressed_bits": 201, "mse": 0.004440578190699787, "ssim": 0.8818088907431588, "psnr": 32.5984584723338, "train_recon_loss": 4.440578190699787, "test_recon_loss": 4.884636009769766}, {"model_id": 249, "latent_dim": 33, "beta": 49.85020000000001, "compression_ratio": 31.448305845920135, "bits_per_pixel": 0.2543857223723185, "compressed_bits": 199, "mse": 0.001, "ssim": 0.8866782826961915, "psnr": 34.3989168361321, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 250, "latent_dim": 34, "beta": 50.050000000000004, "compression_ratio": 31.798215296539812, "bits_per_pixel": 0.2515864467673611, "compressed_bits": 197, "mse": 0.001, "ssim": 0.8623439096450112, "psnr": 31.718990074715972, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 251, "latent_dim": 34, "beta": 50.24980000000001, "compression_ratio": 32.15201801327795, "bits_per_pixel": 0.24881797455749766, "compressed_bits": 195, "mse": 0.008397043044564756, "ssim": 0.8663923098032824, "psnr": 33.68570069208352, "train_recon_loss": 8.397043044564755, "test_recon_loss": 9.236747349021233}, {"model_id": 252, "latent_dim": 34, "beta": 50.449600000000004, "compression_ratio": 32.50975731454464, "bits_per_pixel": 0.2460799667803381, "compressed_bits": 192, "mse": 0.002977146034588026, "ssim": 0.8713626202125828, "psnr": 31.40143073897869, "train_recon_loss": 2.977146034588026, "test_recon_loss": 3.2748606380468286}, {"model_id": 253, "latent_dim": 34, "beta": 50.6494, "compression_ratio": 32.871477000732035, "bits_per_pixel": 0.24337208820345502, "compressed_bits": 190, "mse": 0.001, "ssim": 0.8641669058188597, "psnr": 33.592930966288826, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 254, "latent_dim": 34, "beta": 50.8492, "compression_ratio": 33.23722135957723, "bits_per_pixel": 0.24069400728333806, "compressed_bits": 188, "mse": 0.001, "ssim": 0.8688630258180257, "psnr": 32.91679204845037, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 255, "latent_dim": 34, "beta": 51.049, "compression_ratio": 33.60703517158464, "bits_per_pixel": 0.23804539612480144, "compressed_bits": 186, "mse": 0.001, "ssim": 0.881496727698093, "psnr": 33.303359453246046, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 256, "latent_dim": 34, "beta": 51.2488, "compression_ratio": 33.980963715508764, "bits_per_pixel": 0.23542593044083782, "compressed_bits": 184, "mse": 0.0034211831625268513, "ssim": 0.8772137067069765, "psnr": 33.05599405249926, "train_recon_loss": 3.421183162526851, "test_recon_loss": 3.7633014787795362}, {"model_id": 257, "latent_dim": 34, "beta": 51.448600000000006, "compression_ratio": 34.35905277389801, "bits_per_pixel": 0.23283528951291302, "compressed_bits": 182, "mse": 0.001, "ssim": 0.8731721804818863, "psnr": 32.281198296867636, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 258, "latent_dim": 34, "beta": 51.6484, "compression_ratio": 34.74134863870013, "bits_per_pixel": 0.2302731561516987, "compressed_bits": 180, "mse": 0.003864629665492037, "ssim": 0.8969693138132003, "psnr": 33.282429516588365, "train_recon_loss": 3.864629665492037, "test_recon_loss": 4.2510926320412405}, {"model_id": 259, "latent_dim": 35, "beta": 51.848200000000006, "compression_ratio": 35.12789811693001, "bits_per_pixel": 0.22773921665823701, "compressed_bits": 178, "mse": 0.0047602482556781895, "ssim": 0.8527443459029558, "psnr": 32.33935288941966, "train_recon_loss": 4.7602482556781895, "test_recon_loss": 5.236273081246009}, {"model_id": 260, "latent_dim": 35, "beta": 52.048, "compression_ratio": 35.51874853640066, "bits_per_pixel": 0.22523316078553177, "compressed_bits": 176, "mse": 0.004587938359872879, "ssim": 0.9048077095250402, "psnr": 34.097683511988635, "train_recon_loss": 4.5879383598728785, "test_recon_loss": 5.046732195860167}, {"model_id": 261, "latent_dim": 35, "beta": 52.247800000000005, "compression_ratio": 35.91394775151778, "bits_per_pixel": 0.22275468170056317, "compressed_bits": 174, "mse": 0.001, "ssim": 0.8914139062117503, "psnr": 31.140324255095692, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 262, "latent_dim": 35, "beta": 52.44760000000001, "compression_ratio": 36.31354414913892, "bits_per_pixel": 0.22030347594672053, "compressed_bits": 172, "mse": 0.001, "ssim": 0.8631029871190141, "psnr": 33.66988059561077, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 263, "latent_dim": 35, "beta": 52.647400000000005, "compression_ratio": 36.71758665449782, "bits_per_pixel": 0.2178792434066474, "compressed_bits": 170, "mse": 0.001, "ssim": 0.8787151694261203, "psnr": 30.319595344481993, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 264, "latent_dim": 35, "beta": 52.84720000000001, "compression_ratio": 37.12612473719467, "bits_per_pixel": 0.2154816872654966, "compressed_bits": 168, "mse": 0.0063475987364203885, "ssim": 0.8446246551838471, "psnr": 33.46577272506149, "train_recon_loss": 6.347598736420388, "test_recon_loss": 6.982358610062428}, {"model_id": 265, "latent_dim": 35, "beta": 53.047000000000004, "compression_ratio": 37.5392084172529, "bits_per_pixel": 0.21311051397458944, "compressed_bits": 167, "mse": 0.0072639255629357346, "ssim": 0.8883155117150374, "psnr": 32.2728080177117, "train_recon_loss": 7.263925562935735, "test_recon_loss": 7.990318119229308}, {"model_id": 266, "latent_dim": 35, "beta": 53.24680000000001, "compression_ratio": 37.95688827124359, "bits_per_pixel": 0.210765433215474, "compressed_bits": 165, "mse": 0.008998352196382085, "ssim": 0.8581973363526023, "psnr": 31.346042600799347, "train_recon_loss": 8.998352196382085, "test_recon_loss": 9.898187416020294}, {"model_id": 267, "latent_dim": 36, "beta": 53.44660000000001, "compression_ratio": 38.37921543847785, "bits_per_pixel": 0.2084461578643799, "compressed_bits": 163, "mse": 0.001, "ssim": 0.8496175747501482, "psnr": 32.24063191725432, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 268, "latent_dim": 36, "beta": 53.6464, "compression_ratio": 38.80624162726806, "bits_per_pixel": 0.20615240395706408, "compressed_bits": 161, "mse": 0.004653222577868741, "ssim": 0.8417499329287306, "psnr": 33.66358185818016, "train_recon_loss": 4.653222577868741, "test_recon_loss": 5.118544835655615}, {"model_id": 269, "latent_dim": 36, "beta": 53.8462, "compression_ratio": 39.23801912125899, "bits_per_pixel": 0.20388389065404255, "compressed_bits": 159, "mse": 0.001, "ssim": 0.8607067124633986, "psnr": 32.257312752465715, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 270, "latent_dim": 36, "beta": 54.046, "compression_ratio": 39.6746007858292, "bits_per_pixel": 0.2016403402062058, "compressed_bits": 158, "mse": 0.0010701609846936204, "ssim": 0.85162558143764, "psnr": 33.83786010367744, "train_recon_loss": 1.0701609846936204, "test_recon_loss": 1.1771770831629824}, {"model_id": 271, "latent_dim": 36, "beta": 54.2458, "compression_ratio": 40.116040074563585, "bits_per_pixel": 0.19942147792081222, "compressed_bits": 156, "mse": 0.0027264873284154056, "ssim": 0.8713963619754237, "psnr": 33.30028733981895, "train_recon_loss": 2.7264873284154056, "test_recon_loss": 2.999136061256946}, {"model_id": 272, "latent_dim": 36, "beta": 54.445600000000006, "compression_ratio": 40.56239103579823, "bits_per_pixel": 0.19722703212785514, "compressed_bits": 154, "mse": 0.007136454788782497, "ssim": 0.8666000900761265, "psnr": 30.104924463808068, "train_recon_loss": 7.136454788782498, "test_recon_loss": 7.850100267660747}, {"model_id": 273, "latent_dim": 36, "beta": 54.6454, "compression_ratio": 41.0137083192378, "bits_per_pixel": 0.1950567341468008, "compressed_bits": 152, "mse": 0.0028585778995201825, "ssim": 0.8725745112232496, "psnr": 31.63636882004486, "train_recon_loss": 2.8585778995201823, "test_recon_loss": 3.1444356894722008}, {"model_id": 274, "latent_dim": 36, "beta": 54.845200000000006, "compression_ratio": 41.470047182646624, "bits_per_pixel": 0.1929103182536924, "compressed_bits": 151, "mse": 0.004647126967469288, "ssim": 0.8866602730999434, "psnr": 32.124107141236024, "train_recon_loss": 4.647126967469289, "test_recon_loss": 5.111839664216217}, {"model_id": 275, "latent_dim": 37, "beta": 55.045, "compression_ratio": 41.931463498614384, "bits_per_pixel": 0.19078752164861495, "compressed_bits": 149, "mse": 0.007426276999392357, "ssim": 0.8560966571828923, "psnr": 35.469275768791256, "train_recon_loss": 7.426276999392357, "test_recon_loss": 8.168904699331593}, {"model_id": 276, "latent_dim": 37, "beta": 55.244800000000005, "compression_ratio": 42.39801376139693, "bits_per_pixel": 0.18868808442351936, "compressed_bits": 147, "mse": 0.001, "ssim": 0.8404571453243612, "psnr": 32.11614194753919, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 277, "latent_dim": 37, "beta": 55.44460000000001, "compression_ratio": 42.86975509383315, "bits_per_pixel": 0.18661174953040044, "compressed_bits": 146, "mse": 0.001, "ssim": 0.8460482353100718, "psnr": 31.325799329492014, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 278, "latent_dim": 37, "beta": 55.644400000000005, "compression_ratio": 43.34674525433902, "bits_per_pixel": 0.1845582627498243, "compressed_bits": 144, "mse": 0.0013271912525837583, "ssim": 0.8428949488370964, "psnr": 31.904699938085518, "train_recon_loss": 1.3271912525837584, "test_recon_loss": 1.459910377842134}, {"model_id": 279, "latent_dim": 37, "beta": 55.84420000000001, "compression_ratio": 43.829042643979335, "bits_per_pixel": 0.1825273726598027, "compressed_bits": 143, "mse": 0.014504299774028956, "ssim": 0.8708294820009925, "psnr": 31.760350425793884, "train_recon_loss": 14.504299774028956, "test_recon_loss": 15.95472975143185}, {"model_id": 280, "latent_dim": 37, "beta": 56.044000000000004, "compression_ratio": 44.31670631361803, "bits_per_pixel": 0.18051883060501023, "compressed_bits": 141, "mse": 0.005525498693518427, "ssim": 0.84551504872428, "psnr": 31.26710979792795, "train_recon_loss": 5.525498693518427, "test_recon_loss": 6.078048562870269}, {"model_id": 281, "latent_dim": 37, "beta": 56.24380000000001, "compression_ratio": 44.80979597114829, "bits_per_pixel": 0.17853239066633922, "compressed_bits": 139, "mse": 0.001, "ssim": 0.8682637447377182, "psnr": 31.822978590818877, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 282, "latent_dim": 37, "beta": 56.44360000000001, "compression_ratio": 45.30837198880296, "bits_per_pixel": 0.17656780963079047, "compressed_bits": 138, "mse": 0.0013635010171229654, "ssim": 0.8322973885016665, "psnr": 33.17216404088584, "train_recon_loss": 1.3635010171229653, "test_recon_loss": 1.499851118835262}, {"model_id": 283, "latent_dim": 37, "beta": 56.6434, "compression_ratio": 45.81249541054623, "bits_per_pixel": 0.17462484696169522, "compressed_bits": 136, "mse": 0.008493076554630035, "ssim": 0.8739280628868167, "psnr": 31.036446695559125, "train_recon_loss": 8.493076554630035, "test_recon_loss": 9.342384210093039}, {"model_id": 284, "latent_dim": 38, "beta": 56.8432, "compression_ratio": 46.32222795954776, "bits_per_pixel": 0.17270326476926443, "compressed_bits": 135, "mse": 0.001, "ssim": 0.8673365776869502, "psnr": 32.42959172883958, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 285, "latent_dim": 38, "beta": 57.043, "compression_ratio": 46.837632045739845, "bits_per_pixel": 0.17080282778146225, "compressed_bits": 133, "mse": 0.001, "ssim": 0.8633063976134282, "psnr": 32.1284012337735, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 286, "latent_dim": 38, "beta": 57.2428, "compression_ratio": 47.358770773458545, "bits_per_pixel": 0.1689233033152007, "compressed_bits": 132, "mse": 0.001, "ssim": 0.844615379276459, "psnr": 32.24093807439398, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 287, "latent_dim": 38, "beta": 57.442600000000006, "compression_ratio": 47.88570794917016, "bits_per_pixel": 0.16706446124785, "compressed_bits": 130, "mse": 0.009507862711077378, "ssim": 0.8565258124912468, "psnr": 31.370482857824282, "train_recon_loss": 9.507862711077378, "test_recon_loss": 10.458648982185116}, {"model_id": 288, "latent_dim": 38, "beta": 57.6424, "compression_ratio": 48.41850808928334, "bits_per_pixel": 0.16522607398906353, "compressed_bits": 129, "mse": 0.0029503743876951776, "ssim": 0.8666400507251742, "psnr": 30.402412819528298, "train_recon_loss": 2.950374387695178, "test_recon_loss": 3.245411826464695}, {"model_id": 289, "latent_dim": 38, "beta": 57.842200000000005, "compression_ratio": 48.95723642804835, "bits_per_pixel": 0.16340791645291233, "compressed_bits": 128, "mse": 0.001, "ssim": 0.8441414073751687, "psnr": 31.535141400138542, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 290, "latent_dim": 38, "beta": 58.042, "compression_ratio": 49.50195892554398, "bits_per_pixel": 0.1616097660303266, "compressed_bits": 126, "mse": 0.004441086881659347, "ssim": 0.8424248934728947, "psnr": 31.4393002057554, "train_recon_loss": 4.441086881659348, "test_recon_loss": 4.8851955698252825}, {"model_id": 291, "latent_dim": 38, "beta": 58.241800000000005, "compression_ratio": 50.05274227575367, "bits_per_pixel": 0.15983140256183975, "compressed_bits": 125, "mse": 0.0021416456890454055, "ssim": 0.8607884942749094, "psnr": 31.539247368773328, "train_recon_loss": 2.1416456890454056, "test_recon_loss": 2.3558102579499463}, {"model_id": 292, "latent_dim": 39, "beta": 58.44160000000001, "compression_ratio": 50.609653914731204, "bits_per_pixel": 0.158072608310633, "compressed_bits": 123, "mse": 0.0046230059447758135, "ssim": 0.8566270069339688, "psnr": 29.68140373475179, "train_recon_loss": 4.623005944775813, "test_recon_loss": 5.085306539253395}, {"model_id": 293, "latent_dim": 39, "beta": 58.641400000000004, "compression_ratio": 51.172762028857264, "bits_per_pixel": 0.15633316793587676, "compressed_bits": 122, "mse": 0.001, "ssim": 0.8713626007272083, "psnr": 33.288830919708865, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 294, "latent_dim": 39, "beta": 58.84120000000001, "compression_ratio": 51.74213556318811, "bits_per_pixel": 0.15461286846636443, "compressed_bits": 121, "mse": 0.006962714083816749, "ssim": 0.8444156572845942, "psnr": 30.8424775687147, "train_recon_loss": 6.962714083816749, "test_recon_loss": 7.658985492198424}, {"model_id": 295, "latent_dim": 39, "beta": 59.041000000000004, "compression_ratio": 52.3178442298969, "bits_per_pixel": 0.15291149927443723, "compressed_bits": 119, "mse": 0.001644334172210476, "ssim": 0.8708944332109636, "psnr": 32.639833788585484, "train_recon_loss": 1.644334172210476, "test_recon_loss": 1.8087675894315236}, {"model_id": 296, "latent_dim": 39, "beta": 59.24080000000001, "compression_ratio": 52.89995851680892, "bits_per_pixel": 0.15122885205019596, "compressed_bits": 118, "mse": 0.0010577151473275738, "ssim": 0.8303360912766958, "psnr": 30.524616613326828, "train_recon_loss": 1.0577151473275739, "test_recon_loss": 1.1634866620603312}, {"model_id": 297, "latent_dim": 39, "beta": 59.44060000000001, "compression_ratio": 53.488549696032074, "bits_per_pixel": 0.14956472077599559, "compressed_bits": 117, "mse": 0.0038259639086078885, "ssim": 0.8825280621412228, "psnr": 32.33399281728022, "train_recon_loss": 3.8259639086078887, "test_recon_loss": 4.208560299468678}, {"model_id": 298, "latent_dim": 39, "beta": 59.6404, "compression_ratio": 54.08368983268305, "bits_per_pixel": 0.14791890170122157, "compressed_bits": 115, "mse": 0.001, "ssim": 0.8920339465930821, "psnr": 32.60405176600675, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 299, "latent_dim": 39, "beta": 59.8402, "compression_ratio": 54.68545179371073, "bits_per_pixel": 0.1462911933173434, "compressed_bits": 114, "mse": 0.001, "ssim": 0.8400923356375785, "psnr": 31.76822135476445, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 300, "latent_dim": 40, "beta": 60.04, "compression_ratio": 55.29390925681788, "bits_per_pixel": 0.14468139633324226, "compressed_bits": 113, "mse": 0.007078849098558181, "ssim": 0.8664768275606043, "psnr": 31.38028117239797, "train_recon_loss": 7.07884909855818, "test_recon_loss": 7.786734008413998}, {"model_id": 301, "latent_dim": 40, "beta": 60.2398, "compression_ratio": 55.90913671948193, "bits_per_pixel": 0.14308931365081057, "compressed_bits": 112, "mse": 0.005537018964727339, "ssim": 0.8817352607679058, "psnr": 30.184668031752594, "train_recon_loss": 5.53701896472734, "test_recon_loss": 6.090720861200073}, {"model_id": 302, "latent_dim": 40, "beta": 60.439600000000006, "compression_ratio": 56.53120950807611, "bits_per_pixel": 0.14151475034082034, "compressed_bits": 110, "mse": 0.001, "ssim": 0.8729524336300466, "psnr": 31.14008920380931, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 303, "latent_dim": 40, "beta": 60.6394, "compression_ratio": 57.16020378709236, "bits_per_pixel": 0.1399575136190561, "compressed_bits": 109, "mse": 0.004193465259820879, "ssim": 0.8259178316376334, "psnr": 31.56075042838433, "train_recon_loss": 4.193465259820878, "test_recon_loss": 4.6128117858029665}, {"model_id": 304, "latent_dim": 40, "beta": 60.839200000000005, "compression_ratio": 57.79619656846649, "bits_per_pixel": 0.13841741282271136, "compressed_bits": 108, "mse": 0.001, "ssim": 0.8463008221756864, "psnr": 32.04499553481764, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 305, "latent_dim": 40, "beta": 61.039, "compression_ratio": 58.43926572100727, "bits_per_pixel": 0.13689425938704472, "compressed_bits": 107, "mse": 0.006319645044589472, "ssim": 0.866960643780805, "psnr": 32.27225954246988, "train_recon_loss": 6.319645044589472, "test_recon_loss": 6.9516095490484195}, {"model_id": 306, "latent_dim": 40, "beta": 61.238800000000005, "compression_ratio": 59.08948997993051, "bits_per_pixel": 0.13538786682229217, "compressed_bits": 106, "mse": 0.001, "ssim": 0.8348080980489013, "psnr": 32.06150506149555, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 307, "latent_dim": 40, "beta": 61.43860000000001, "compression_ratio": 59.746948956499125, "bits_per_pixel": 0.13389805069083413, "compressed_bits": 104, "mse": 0.0030152897900519327, "ssim": 0.877662487406648, "psnr": 31.71768459995655, "train_recon_loss": 3.015289790051933, "test_recon_loss": 3.316818769057126}, {"model_id": 308, "latent_dim": 40, "beta": 61.638400000000004, "compression_ratio": 60.41172314777032, "bits_per_pixel": 0.1324246285846138, "compressed_bits": 103, "mse": 0.001, "ssim": 0.7914302162237743, "psnr": 31.42683429382703, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 309, "latent_dim": 41, "beta": 61.83820000000001, "compression_ratio": 61.08389394645157, "bits_per_pixel": 0.1309674201028032, "compressed_bits": 102, "mse": 0.001, "ssim": 0.8297204877858815, "psnr": 31.475086543959556, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 310, "latent_dim": 41, "beta": 62.038000000000004, "compression_ratio": 61.763543650866005, "bits_per_pixel": 0.12952624682971586, "compressed_bits": 101, "mse": 0.0011698198377738393, "ssim": 0.8487006543342588, "psnr": 32.27509269331683, "train_recon_loss": 1.1698198377738394, "test_recon_loss": 1.2868018215512234}, {"model_id": 311, "latent_dim": 41, "beta": 62.23780000000001, "compression_ratio": 62.450755475028636, "bits_per_pixel": 0.12810093231296224, "compressed_bits": 100, "mse": 0.001, "ssim": 0.8402502931511403, "psnr": 30.138508326714817, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 312, "latent_dim": 41, "beta": 62.43760000000001, "compression_ratio": 63.14561355883495, "bits_per_pixel": 0.12669130204184528, "compressed_bits": 99, "mse": 0.007364595091519923, "ssim": 0.8156336043359614, "psnr": 30.92418958008635, "train_recon_loss": 7.364595091519924, "test_recon_loss": 8.101054600671915}, {"model_id": 313, "latent_dim": 41, "beta": 62.6374, "compression_ratio": 63.84820297836268, "bits_per_pixel": 0.1252971834259939, "compressed_bits": 98, "mse": 0.001, "ssim": 0.8317723980921345, "psnr": 32.44633190200236, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 314, "latent_dim": 41, "beta": 62.8372, "compression_ratio": 64.55860975628808, "bits_per_pixel": 0.12391840577423202, "compressed_bits": 97, "mse": 0.002313640572402616, "ssim": 0.8371270905392306, "psnr": 32.28460817087444, "train_recon_loss": 2.313640572402616, "test_recon_loss": 2.5450046296428774}, {"model_id": 315, "latent_dim": 41, "beta": 63.037, "compression_ratio": 65.27692087241847, "bits_per_pixel": 0.1225548002736791, "compressed_bits": 96, "mse": 0.001, "ssim": 0.8001223003186437, "psnr": 31.086363230682558, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 316, "latent_dim": 41, "beta": 63.2368, "compression_ratio": 66.00322427434168, "bits_per_pixel": 0.12120619996908163, "compressed_bits": 95, "mse": 0.0073436452016432795, "ssim": 0.8111314824269892, "psnr": 33.299860179007126, "train_recon_loss": 7.343645201643279, "test_recon_loss": 8.078009721807607}, {"model_id": 317, "latent_dim": 42, "beta": 63.436600000000006, "compression_ratio": 66.73760888819389, "bits_per_pixel": 0.11987243974237181, "compressed_bits": 93, "mse": 0.006475870203791191, "ssim": 0.8778131041569085, "psnr": 32.562335588669946, "train_recon_loss": 6.4758702037911915, "test_recon_loss": 7.12345722417031}, {"model_id": 318, "latent_dim": 42, "beta": 63.6364, "compression_ratio": 67.4801646295477, "bits_per_pixel": 0.11855335629245073, "compressed_bits": 92, "mse": 0.004690442795653103, "ssim": 0.8271159448038223, "psnr": 32.7995053905407, "train_recon_loss": 4.6904427956531025, "test_recon_loss": 5.159487075218413}, {"model_id": 319, "latent_dim": 42, "beta": 63.836200000000005, "compression_ratio": 68.23098241442099, "bits_per_pixel": 0.11724878811519437, "compressed_bits": 91, "mse": 0.0052837882654202155, "ssim": 0.8696235510197808, "psnr": 33.19985001797631, "train_recon_loss": 5.283788265420216, "test_recon_loss": 5.8121670919622375}, {"model_id": 320, "latent_dim": 42, "beta": 64.036, "compression_ratio": 68.99015417040823, "bits_per_pixel": 0.11595857548367995, "compressed_bits": 90, "mse": 0.005708963046571019, "ssim": 0.8324132137530476, "psnr": 31.18473705819215, "train_recon_loss": 5.708963046571019, "test_recon_loss": 6.2798593512281204}, {"model_id": 321, "latent_dim": 42, "beta": 64.2358, "compression_ratio": 69.75777284793601, "bits_per_pixel": 0.11468256042862905, "compressed_bits": 89, "mse": 0.005633661393222368, "ssim": 0.8496661244749959, "psnr": 31.270093317023576, "train_recon_loss": 5.633661393222368, "test_recon_loss": 6.197027532544604}, {"model_id": 322, "latent_dim": 42, "beta": 64.4356, "compression_ratio": 70.53393243164349, "bits_per_pixel": 0.11342058671906653, "compressed_bits": 88, "mse": 0.001, "ssim": 0.8307573529845712, "psnr": 31.874769853793993, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 323, "latent_dim": 42, "beta": 64.63539999999999, "compression_ratio": 71.31872795188941, "bits_per_pixel": 0.11217249984319244, "compressed_bits": 87, "mse": 0.001, "ssim": 0.8204940305195264, "psnr": 30.85419274035406, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 324, "latent_dim": 42, "beta": 64.8352, "compression_ratio": 72.11225549638756, "bits_per_pixel": 0.11093814698946364, "compressed_bits": 86, "mse": 0.001, "ssim": 0.8300224449850451, "psnr": 32.4554470191816, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 325, "latent_dim": 43, "beta": 65.035, "compression_ratio": 72.9146122219713, "bits_per_pixel": 0.10971737702788423, "compressed_bits": 86, "mse": 0.0034374091714033726, "ssim": 0.8492117761898905, "psnr": 30.740132585516225, "train_recon_loss": 3.4374091714033725, "test_recon_loss": 3.78115008854371}, {"model_id": 326, "latent_dim": 43, "beta": 65.23479999999999, "compression_ratio": 73.72589636648894, "bits_per_pixel": 0.10851004049150206, "compressed_bits": 85, "mse": 0.0020238312826288255, "ssim": 0.8367439085026628, "psnr": 31.34162018357119, "train_recon_loss": 2.0238312826288256, "test_recon_loss": 2.226214410891708}, {"model_id": 327, "latent_dim": 43, "beta": 65.4346, "compression_ratio": 74.54620726083202, "bits_per_pixel": 0.10731598955810795, "compressed_bits": 84, "mse": 0.005838656090087526, "ssim": 0.8304564020829402, "psnr": 30.417797645616883, "train_recon_loss": 5.838656090087526, "test_recon_loss": 6.422521699096279}, {"model_id": 328, "latent_dim": 43, "beta": 65.6344, "compression_ratio": 75.3756453410968, "bits_per_pixel": 0.1061350780321371, "compressed_bits": 83, "mse": 0.004545176605235718, "ssim": 0.824668870883114, "psnr": 31.253026216295474, "train_recon_loss": 4.545176605235718, "test_recon_loss": 4.99969426575929}, {"model_id": 329, "latent_dim": 43, "beta": 65.8342, "compression_ratio": 76.21431216088126, "bits_per_pixel": 0.10496716132676957, "compressed_bits": 82, "mse": 0.0024313150609725632, "ssim": 0.8287410699268851, "psnr": 31.27844313337648, "train_recon_loss": 2.431315060972563, "test_recon_loss": 2.6744465670698196}, {"model_id": 330, "latent_dim": 43, "beta": 66.03399999999999, "compression_ratio": 77.06231040371921, "bits_per_pixel": 0.10381209644622724, "compressed_bits": 81, "mse": 0.001, "ssim": 0.8111626870372957, "psnr": 31.376004383668615, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 331, "latent_dim": 43, "beta": 66.2338, "compression_ratio": 77.91974389565237, "bits_per_pixel": 0.10266974196826602, "compressed_bits": 80, "mse": 0.0034224695500815285, "ssim": 0.8516509541253952, "psnr": 29.83100331141653, "train_recon_loss": 3.4224695500815288, "test_recon_loss": 3.7647165050896816}, {"model_id": 332, "latent_dim": 43, "beta": 66.4336, "compression_ratio": 78.78671761794244, "bits_per_pixel": 0.10153995802686067, "compressed_bits": 79, "mse": 0.004814261427627701, "ssim": 0.8412283272198694, "psnr": 33.00687132002228, "train_recon_loss": 4.8142614276277005, "test_recon_loss": 5.295687570390471}, {"model_id": 333, "latent_dim": 43, "beta": 66.63340000000001, "compression_ratio": 79.66333771992477, "bits_per_pixel": 0.10042260629507999, "compressed_bits": 78, "mse": 0.001, "ssim": 0.8165295560257766, "psnr": 30.71293006343421, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 334, "latent_dim": 44, "beta": 66.83319999999999, "compression_ratio": 80.54971153200489, "bits_per_pixel": 0.09931754996815044, "compressed_bits": 77, "mse": 0.001, "ssim": 0.8133660907029411, "psnr": 30.941628275327098, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 335, "latent_dim": 44, "beta": 67.033, "compression_ratio": 81.44594757879958, "bits_per_pixel": 0.09822465374670654, "compressed_bits": 77, "mse": 0.001, "ssim": 0.7891698234923633, "psnr": 30.216219395180374, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 336, "latent_dim": 44, "beta": 67.2328, "compression_ratio": 82.35215559242438, "bits_per_pixel": 0.09714378382022491, "compressed_bits": 76, "mse": 0.0028486113745767594, "ssim": 0.8221127885194, "psnr": 31.734409026571818, "train_recon_loss": 2.8486113745767594, "test_recon_loss": 3.1334725120344356}, {"model_id": 337, "latent_dim": 44, "beta": 67.4326, "compression_ratio": 83.26844652592887, "bits_per_pixel": 0.09607480785064111, "compressed_bits": 75, "mse": 0.001, "ssim": 0.8528182204606288, "psnr": 29.614054293576796, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 338, "latent_dim": 44, "beta": 67.6324, "compression_ratio": 84.1949325668811, "bits_per_pixel": 0.09501759495614677, "compressed_bits": 74, "mse": 0.01308383711657754, "ssim": 0.7604962306731354, "psnr": 30.56877394494604, "train_recon_loss": 13.08383711657754, "test_recon_loss": 14.392220828235294}, {"model_id": 339, "latent_dim": 44, "beta": 67.83219999999999, "compression_ratio": 85.13172715110385, "bits_per_pixel": 0.09397201569516458, "compressed_bits": 73, "mse": 0.001, "ssim": 0.764461075515872, "psnr": 30.581370885877707, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 340, "latent_dim": 44, "beta": 68.032, "compression_ratio": 86.07894497656305, "bits_per_pixel": 0.09293794205050006, "compressed_bits": 72, "mse": 0.001, "ssim": 0.8436305966410007, "psnr": 30.908840894325223, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 341, "latent_dim": 44, "beta": 68.23179999999999, "compression_ratio": 87.03670201741103, "bits_per_pixel": 0.09191524741366763, "compressed_bits": 72, "mse": 0.001, "ssim": 0.8176932830601058, "psnr": 31.093706777889697, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 342, "latent_dim": 45, "beta": 68.4316, "compression_ratio": 88.00511553818617, "bits_per_pixel": 0.09090380656938893, "compressed_bits": 71, "mse": 0.008186802679544471, "ssim": 0.8318940560216082, "psnr": 31.18028042338143, "train_recon_loss": 8.18680267954447, "test_recon_loss": 9.005482947498917}, {"model_id": 343, "latent_dim": 45, "beta": 68.6314, "compression_ratio": 88.98430410817028, "bits_per_pixel": 0.08990349568026193, "compressed_bits": 70, "mse": 0.008056453149597962, "ssim": 0.8190953287969467, "psnr": 30.028547987980176, "train_recon_loss": 8.056453149597962, "test_recon_loss": 8.862098464557759}, {"model_id": 344, "latent_dim": 45, "beta": 68.8312, "compression_ratio": 89.97438761590567, "bits_per_pixel": 0.08891419227159886, "compressed_bits": 69, "mse": 0.005376101792602781, "ssim": 0.8054681713070544, "psnr": 30.3615381397883, "train_recon_loss": 5.376101792602781, "test_recon_loss": 5.913711971863059}, {"model_id": 345, "latent_dim": 45, "beta": 69.03099999999999, "compression_ratio": 90.9754872838742, "bits_per_pixel": 0.08793577521643058, "compressed_bits": 68, "mse": 0.001, "ssim": 0.8474416216818915, "psnr": 29.579180402137524, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 346, "latent_dim": 45, "beta": 69.2308, "compression_ratio": 91.98772568333918, "bits_per_pixel": 0.0869681247206763, "compressed_bits": 68, "mse": 0.004456029077016405, "ssim": 0.8461935260167412, "psnr": 33.01300538683158, "train_recon_loss": 4.456029077016405, "test_recon_loss": 4.901631984718045}, {"model_id": 347, "latent_dim": 45, "beta": 69.4306, "compression_ratio": 93.01122674935246, "bits_per_pixel": 0.08601112230847655, "compressed_bits": 67, "mse": 0.002793298630159562, "ssim": 0.8049548955354713, "psnr": 32.87024294707588, "train_recon_loss": 2.7932986301595624, "test_recon_loss": 3.0726284931755186}, {"model_id": 348, "latent_dim": 45, "beta": 69.63040000000001, "compression_ratio": 94.04611579592888, "bits_per_pixel": 0.08506465080768713, "compressed_bits": 66, "mse": 0.014444515550389551, "ssim": 0.8570828784013972, "psnr": 31.032029502937732, "train_recon_loss": 14.444515550389552, "test_recon_loss": 15.888967105428506}, {"model_id": 349, "latent_dim": 45, "beta": 69.83019999999999, "compression_ratio": 95.09251953138921, "bits_per_pixel": 0.08412859433553309, "compressed_bits": 65, "mse": 0.010994588814628404, "ssim": 0.8178480506405632, "psnr": 29.71016175003252, "train_recon_loss": 10.994588814628404, "test_recon_loss": 12.094047696091245}, {"model_id": 350, "latent_dim": 46, "beta": 70.03, "compression_ratio": 96.15056607387366, "bits_per_pixel": 0.08320283828442052, "compressed_bits": 65, "mse": 0.005422261384017162, "ssim": 0.8468039881218521, "psnr": 29.802604667814204, "train_recon_loss": 5.422261384017162, "test_recon_loss": 5.964487522418878}, {"model_id": 351, "latent_dim": 46, "beta": 70.2298, "compression_ratio": 97.22038496702861, "bits_per_pixel": 0.08228726930790416, "compressed_bits": 64, "mse": 0.001, "ssim": 0.812104174239545, "psnr": 29.490689407475, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 352, "latent_dim": 46, "beta": 70.4296, "compression_ratio": 98.30210719586725, "bits_per_pixel": 0.0813817753068098, "compressed_bits": 63, "mse": 0.0031869094917721876, "ssim": 0.8135772036763216, "psnr": 29.692863594553103, "train_recon_loss": 3.1869094917721874, "test_recon_loss": 3.505600440949406}, {"model_id": 353, "latent_dim": 46, "beta": 70.6294, "compression_ratio": 99.39586520280692, "bits_per_pixel": 0.08048624541550932, "compressed_bits": 63, "mse": 0.0062775121649294285, "ssim": 0.8216344099378622, "psnr": 28.890799447286334, "train_recon_loss": 6.277512164929428, "test_recon_loss": 6.905263381422372}, {"model_id": 354, "latent_dim": 46, "beta": 70.82919999999999, "compression_ratio": 100.50179290388509, "bits_per_pixel": 0.07960056998834639, "compressed_bits": 62, "mse": 0.0033469498019645175, "ssim": 0.8007107565418685, "psnr": 29.02619741286218, "train_recon_loss": 3.3469498019645174, "test_recon_loss": 3.6816447821609692}, {"model_id": 355, "latent_dim": 46, "beta": 71.029, "compression_ratio": 101.62002570515547, "bits_per_pixel": 0.0787246405862121, "compressed_bits": 61, "mse": 0.007997431029737306, "ssim": 0.8271761696370087, "psnr": 28.264793929665593, "train_recon_loss": 7.997431029737306, "test_recon_loss": 8.797174132711037}, {"model_id": 356, "latent_dim": 46, "beta": 71.22879999999999, "compression_ratio": 102.75070051926669, "bits_per_pixel": 0.07785834996326792, "compressed_bits": 61, "mse": 0.004744612306934161, "ssim": 0.8086706357296563, "psnr": 29.51489204486291, "train_recon_loss": 4.744612306934161, "test_recon_loss": 5.219073537627577}, {"model_id": 357, "latent_dim": 46, "beta": 71.4286, "compression_ratio": 103.8939557822254, "bits_per_pixel": 0.07700159205381486, "compressed_bits": 60, "mse": 0.007651028720095119, "ssim": 0.8004148738825734, "psnr": 31.689746122923673, "train_recon_loss": 7.651028720095119, "test_recon_loss": 8.41613159210463}, {"model_id": 358, "latent_dim": 46, "beta": 71.6284, "compression_ratio": 105.04993147034597, "bits_per_pixel": 0.07615426195930723, "compressed_bits": 59, "mse": 0.001, "ssim": 0.7820556937106903, "psnr": 29.570597354253653, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 359, "latent_dim": 47, "beta": 71.8282, "compression_ratio": 106.21876911738875, "bits_per_pixel": 0.07531625593550909, "compressed_bits": 59, "mse": 0.001, "ssim": 0.8196659148140858, "psnr": 28.795649877346637, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 360, "latent_dim": 47, "beta": 72.02799999999999, "compression_ratio": 107.40061183188861, "bits_per_pixel": 0.07448747137979243, "compressed_bits": 58, "mse": 0.001, "ssim": 0.8243370940715258, "psnr": 29.01899393384458, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 361, "latent_dim": 47, "beta": 72.2278, "compression_ratio": 108.59560431467727, "bits_per_pixel": 0.07366780681857449, "compressed_bits": 57, "mse": 0.001, "ssim": 0.822714629870774, "psnr": 29.279892121090786, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 362, "latent_dim": 47, "beta": 72.4276, "compression_ratio": 109.8038928765996, "bits_per_pixel": 0.07285716189489386, "compressed_bits": 57, "mse": 0.013506367373049577, "ssim": 0.8095378842515798, "psnr": 29.009789378392327, "train_recon_loss": 13.506367373049578, "test_recon_loss": 14.857004110354534}, {"model_id": 363, "latent_dim": 47, "beta": 72.62740000000001, "compression_ratio": 111.0256254564275, "bits_per_pixel": 0.07205543735612312, "compressed_bits": 56, "mse": 0.005201004425424158, "ssim": 0.823854601519547, "psnr": 31.065025117760378, "train_recon_loss": 5.201004425424158, "test_recon_loss": 5.721104867966574}, {"model_id": 364, "latent_dim": 47, "beta": 72.82719999999999, "compression_ratio": 112.26095163897304, "bits_per_pixel": 0.07126253504181665, "compressed_bits": 55, "mse": 0.012053752184791867, "ssim": 0.7997453097793807, "psnr": 31.013094463964855, "train_recon_loss": 12.053752184791866, "test_recon_loss": 13.259127403271053}, {"model_id": 365, "latent_dim": 47, "beta": 73.027, "compression_ratio": 113.51002267340317, "bits_per_pixel": 0.07047835787169215, "compressed_bits": 55, "mse": 0.002630486792441185, "ssim": 0.8369860431289022, "psnr": 28.27025321387765, "train_recon_loss": 2.630486792441185, "test_recon_loss": 2.8935354716853037}, {"model_id": 366, "latent_dim": 47, "beta": 73.2268, "compression_ratio": 114.77299149175784, "bits_per_pixel": 0.06970280983374474, "compressed_bits": 54, "mse": 0.009897286523305335, "ssim": 0.8441400811078927, "psnr": 31.211477052317182, "train_recon_loss": 9.897286523305334, "test_recon_loss": 10.887015175635868}, {"model_id": 367, "latent_dim": 48, "beta": 73.4266, "compression_ratio": 116.050012727675, "bits_per_pixel": 0.06893579597249111, "compressed_bits": 54, "mse": 0.0031474334809900857, "ssim": 0.8333708664595566, "psnr": 28.544867232414163, "train_recon_loss": 3.147433480990086, "test_recon_loss": 3.462176829089094}, {"model_id": 368, "latent_dim": 48, "beta": 73.6264, "compression_ratio": 117.3412427353231, "bits_per_pixel": 0.06817722237734379, "compressed_bits": 53, "mse": 0.003840174768339676, "ssim": 0.8211267873621063, "psnr": 30.49138377555665, "train_recon_loss": 3.840174768339676, "test_recon_loss": 4.224192245173644}, {"model_id": 369, "latent_dim": 48, "beta": 73.82619999999999, "compression_ratio": 118.64683960854461, "bits_per_pixel": 0.06742699617111304, "compressed_bits": 52, "mse": 0.005367180938165876, "ssim": 0.8002493048723655, "psnr": 29.73935731891007, "train_recon_loss": 5.367180938165876, "test_recon_loss": 5.903899031982463}, {"model_id": 370, "latent_dim": 48, "beta": 74.026, "compression_ratio": 119.96696320021252, "bits_per_pixel": 0.06668502549863518, "compressed_bits": 52, "mse": 0.001, "ssim": 0.7892929836448639, "psnr": 29.296396480800748, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 371, "latent_dim": 48, "beta": 74.22579999999999, "compression_ratio": 121.30177514180228, "bits_per_pixel": 0.06595121951552618, "compressed_bits": 51, "mse": 0.012521403831338436, "ssim": 0.8140636567288009, "psnr": 27.71511016055149, "train_recon_loss": 12.521403831338436, "test_recon_loss": 13.77354421447228}, {"model_id": 372, "latent_dim": 48, "beta": 74.4256, "compression_ratio": 122.65143886318097, "bits_per_pixel": 0.06522548837705922, "compressed_bits": 51, "mse": 0.010106288955542893, "ssim": 0.8341555552667391, "psnr": 29.09363698463544, "train_recon_loss": 10.106288955542892, "test_recon_loss": 11.116917851097183}, {"model_id": 373, "latent_dim": 48, "beta": 74.6254, "compression_ratio": 124.0161196126178, "bits_per_pixel": 0.06450774322716395, "compressed_bits": 50, "mse": 0.004958129020582777, "ssim": 0.7928230339792027, "psnr": 28.387732719536228, "train_recon_loss": 4.958129020582777, "test_recon_loss": 5.453941922641055}, {"model_id": 374, "latent_dim": 48, "beta": 74.8252, "compression_ratio": 125.39598447701604, "bits_per_pixel": 0.0637978961875476, "compressed_bits": 50, "mse": 0.001, "ssim": 0.7938137895122198, "psnr": 28.533471096270816, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 375, "latent_dim": 49, "beta": 75.02499999999999, "compression_ratio": 126.7912024023708, "bits_per_pixel": 0.06309586034693533, "compressed_bits": 49, "mse": 0.0021913661586191032, "ssim": 0.8149914961889394, "psnr": 28.78925770574355, "train_recon_loss": 2.191366158619103, "test_recon_loss": 2.4105027744810137}, {"model_id": 376, "latent_dim": 49, "beta": 75.2248, "compression_ratio": 128.20194421445404, "bits_per_pixel": 0.06240154975042918, "compressed_bits": 48, "mse": 0.001, "ssim": 0.8068984598857493, "psnr": 29.750753777239495, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 377, "latent_dim": 49, "beta": 75.4246, "compression_ratio": 129.62838263973023, "bits_per_pixel": 0.06171487938898385, "compressed_bits": 48, "mse": 0.0013059439426674375, "ssim": 0.7962931104061332, "psnr": 28.077669672335958, "train_recon_loss": 1.3059439426674375, "test_recon_loss": 1.4365383369341813}, {"model_id": 378, "latent_dim": 49, "beta": 75.62440000000001, "compression_ratio": 131.07069232650372, "bits_per_pixel": 0.06103576518899889, "compressed_bits": 47, "mse": 0.01493769392772943, "ssim": 0.774758142107487, "psnr": 29.537219696221992, "train_recon_loss": 14.93769392772943, "test_recon_loss": 16.431463320502374}, {"model_id": 379, "latent_dim": 49, "beta": 75.82419999999999, "compression_ratio": 132.52904986630287, "bits_per_pixel": 0.06036412400202454, "compressed_bits": 47, "mse": 0.003870868381224279, "ssim": 0.803973033342889, "psnr": 28.4549993524385, "train_recon_loss": 3.870868381224279, "test_recon_loss": 4.257955219346707}, {"model_id": 380, "latent_dim": 49, "beta": 76.024, "compression_ratio": 134.00363381550096, "bits_per_pixel": 0.059699873594581544, "compressed_bits": 46, "mse": 0.012265736323574278, "ssim": 0.8002800889988624, "psnr": 28.813145974939495, "train_recon_loss": 12.265736323574277, "test_recon_loss": 13.492309955931706}, {"model_id": 381, "latent_dim": 49, "beta": 76.2238, "compression_ratio": 135.49462471717794, "bits_per_pixel": 0.05904293263809279, "compressed_bits": 46, "mse": 0.001, "ssim": 0.7970570181014975, "psnr": 27.554486568453797, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 382, "latent_dim": 49, "beta": 76.4236, "compression_ratio": 137.00220512322574, "bits_per_pixel": 0.05839322069892563, "compressed_bits": 45, "mse": 0.001, "ssim": 0.7779278322868513, "psnr": 27.78571905728054, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 383, "latent_dim": 49, "beta": 76.6234, "compression_ratio": 138.52655961669922, "bits_per_pixel": 0.05775065822854385, "compressed_bits": 45, "mse": 0.014689426250784775, "ssim": 0.7842885240915798, "psnr": 28.78324356708261, "train_recon_loss": 14.689426250784775, "test_recon_loss": 16.158368875863253}, {"model_id": 384, "latent_dim": 50, "beta": 76.8232, "compression_ratio": 140.06787483441553, "bits_per_pixel": 0.05711516655376819, "compressed_bits": 44, "mse": 0.001, "ssim": 0.7876714393021904, "psnr": 28.841167528133507, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 385, "latent_dim": 50, "beta": 77.023, "compression_ratio": 141.62633948980599, "bits_per_pixel": 0.05648666786714364, "compressed_bits": 44, "mse": 0.0054262373038634635, "ssim": 0.7871637727636648, "psnr": 29.04558829716806, "train_recon_loss": 5.426237303863464, "test_recon_loss": 5.96886103424981}, {"model_id": 386, "latent_dim": 50, "beta": 77.22279999999999, "compression_ratio": 143.20214439602108, "bits_per_pixel": 0.055865085217413006, "compressed_bits": 43, "mse": 0.001778664400593097, "ssim": 0.8189560764755022, "psnr": 27.577822015378644, "train_recon_loss": 1.778664400593097, "test_recon_loss": 1.9565308406524067}, {"model_id": 387, "latent_dim": 50, "beta": 77.4226, "compression_ratio": 144.79548248929305, "bits_per_pixel": 0.05525034250009535, "compressed_bits": 43, "mse": 0.00222257912300989, "ssim": 0.785619261236022, "psnr": 28.2921673217565, "train_recon_loss": 2.2225791230098904, "test_recon_loss": 2.4448370353108793}, {"model_id": 388, "latent_dim": 50, "beta": 77.6224, "compression_ratio": 146.4065488525583, "bits_per_pixel": 0.05464236444816798, "compressed_bits": 42, "mse": 0.002865439780502141, "ssim": 0.7581911127981528, "psnr": 30.382895175921714, "train_recon_loss": 2.865439780502141, "test_recon_loss": 3.1519837585523547}, {"model_id": 389, "latent_dim": 50, "beta": 77.82220000000001, "compression_ratio": 148.03554073934293, "bits_per_pixel": 0.05404107662285092, "compressed_bits": 42, "mse": 0.004169220534812302, "ssim": 0.774232502463558, "psnr": 29.236864243881566, "train_recon_loss": 4.169220534812301, "test_recon_loss": 4.586142588293532}, {"model_id": 390, "latent_dim": 50, "beta": 78.02199999999999, "compression_ratio": 149.68265759791325, "bits_per_pixel": 0.053446405404493094, "compressed_bits": 41, "mse": 0.011140313245660796, "ssim": 0.7759931334832794, "psnr": 27.195614017864543, "train_recon_loss": 11.140313245660796, "test_recon_loss": 12.254344570226875}, {"model_id": 391, "latent_dim": 50, "beta": 78.2218, "compression_ratio": 151.34810109569628, "bits_per_pixel": 0.05285827798355831, "compressed_bits": 41, "mse": 0.016212958811924333, "ssim": 0.7748030616340364, "psnr": 26.648499346664345, "train_recon_loss": 16.212958811924334, "test_recon_loss": 17.834254693116765}, {"model_id": 392, "latent_dim": 51, "beta": 78.4216, "compression_ratio": 153.0320751439707, "bits_per_pixel": 0.0522766223517109, "compressed_bits": 40, "mse": 0.0038572589935410967, "ssim": 0.7714824062068631, "psnr": 29.60067306840222, "train_recon_loss": 3.857258993541097, "test_recon_loss": 4.2429848928952065}, {"model_id": 393, "latent_dim": 51, "beta": 78.62140000000001, "compression_ratio": 154.7347859228333, "bits_per_pixel": 0.05170136729299915, "compressed_bits": 40, "mse": 0.001, "ssim": 0.8048360498768693, "psnr": 28.30137327394515, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 394, "latent_dim": 51, "beta": 78.8212, "compression_ratio": 156.45644190644288, "bits_per_pixel": 0.05113244237513598, "compressed_bits": 40, "mse": 0.001, "ssim": 0.7503066179495065, "psnr": 26.840829269381672, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 395, "latent_dim": 51, "beta": 79.021, "compression_ratio": 158.19725388854525, "bits_per_pixel": 0.05056977794087527, "compressed_bits": 39, "mse": 0.006708551721554277, "ssim": 0.7584437568246616, "psnr": 28.101244471415086, "train_recon_loss": 6.708551721554278, "test_recon_loss": 7.379406893709705}, {"model_id": 396, "latent_dim": 51, "beta": 79.2208, "compression_ratio": 159.95743500828158, "bits_per_pixel": 0.05001330509948357, "compressed_bits": 39, "mse": 0.0030225725689543726, "ssim": 0.7450931827268302, "psnr": 27.704005068287223, "train_recon_loss": 3.0225725689543728, "test_recon_loss": 3.3248298258498097}, {"model_id": 397, "latent_dim": 51, "beta": 79.4206, "compression_ratio": 161.73720077628533, "bits_per_pixel": 0.04946295571830496, "compressed_bits": 38, "mse": 0.009557811705898865, "ssim": 0.7346878828638903, "psnr": 27.550678263782647, "train_recon_loss": 9.557811705898866, "test_recon_loss": 10.513592876488751}, {"model_id": 398, "latent_dim": 51, "beta": 79.6204, "compression_ratio": 163.53676910106796, "bits_per_pixel": 0.04891866241441942, "compressed_bits": 38, "mse": 0.005001226439218354, "ssim": 0.7379508360179731, "psnr": 26.695241082385042, "train_recon_loss": 5.001226439218353, "test_recon_loss": 5.501349083140189}, {"model_id": 399, "latent_dim": 51, "beta": 79.8202, "compression_ratio": 165.3563603156992, "bits_per_pixel": 0.04838035854639253, "compressed_bits": 37, "mse": 0.010411298253741146, "ssim": 0.7541299541507034, "psnr": 29.540807246740112, "train_recon_loss": 10.411298253741146, "test_recon_loss": 11.45242807911526}, {"model_id": 400, "latent_dim": 52, "beta": 80.02, "compression_ratio": 167.1961972047837, "bits_per_pixel": 0.04784797820611622, "compressed_bits": 37, "mse": 0.007515142971986591, "ssim": 0.7678393434766823, "psnr": 26.881239357972945, "train_recon_loss": 7.515142971986591, "test_recon_loss": 8.26665726918525}, {"model_id": 401, "latent_dim": 52, "beta": 80.21979999999999, "compression_ratio": 169.0565050317384, "bits_per_pixel": 0.047321456210739084, "compressed_bits": 37, "mse": 0.014216059551592462, "ssim": 0.7887450237948307, "psnr": 27.51883855416622, "train_recon_loss": 14.216059551592462, "test_recon_loss": 15.63766550675171}, {"model_id": 402, "latent_dim": 52, "beta": 80.4196, "compression_ratio": 170.9375115663722, "bits_per_pixel": 0.046800728094685835, "compressed_bits": 36, "mse": 0.001, "ssim": 0.7354476603633542, "psnr": 27.680324892100877, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 403, "latent_dim": 52, "beta": 80.6194, "compression_ratio": 172.83944711277442, "bits_per_pixel": 0.04628573010176406, "compressed_bits": 36, "mse": 0.007114246795970712, "ssim": 0.7338361079963033, "psnr": 27.528165338825385, "train_recon_loss": 7.114246795970712, "test_recon_loss": 7.825671475567783}, {"model_id": 404, "latent_dim": 52, "beta": 80.81920000000001, "compression_ratio": 174.7625445375117, "bits_per_pixel": 0.04577639917735833, "compressed_bits": 35, "mse": 0.004103371614944806, "ssim": 0.7784009877330162, "psnr": 28.032449308285955, "train_recon_loss": 4.103371614944805, "test_recon_loss": 4.5137087764392865}, {"model_id": 405, "latent_dim": 52, "beta": 81.01899999999999, "compression_ratio": 176.70703929813968, "bits_per_pixel": 0.045272672960709956, "compressed_bits": 35, "mse": 0.001, "ssim": 0.7649770509725945, "psnr": 28.05001942033103, "train_recon_loss": 1.0, "test_recon_loss": 1.1}, {"model_id": 406, "latent_dim": 52, "beta": 81.2188, "compression_ratio": 178.6731694720315, "bits_per_pixel": 0.044774489777281726, "compressed_bits": 35, "mse": 0.009644798060338033, "ssim": 0.7394944992251602, "psnr": 29.227098717245855, "train_recon_loss": 9.644798060338033, "test_recon_loss": 10.609277866371835}, {"model_id": 407, "latent_dim": 52, "beta": 81.4186, "compression_ratio": 180.66117578552743, "bits_per_pixel": 0.04428178863120668, "compressed_bits": 34, "mse": 0.021953972254191524, "ssim": 0.7306384186273625, "psnr": 26.63483981564784, "train_recon_loss": 21.953972254191523, "test_recon_loss": 24.149369479610677}, {"model_id": 408, "latent_dim": 52, "beta": 81.61840000000001, "compression_ratio": 182.67130164340796, "bits_per_pixel": 0.0437945091978201, "compressed_bits": 34, "mse": 0.011996630250297832, "ssim": 0.7534819425781185, "psnr": 27.70377116300048, "train_recon_loss": 11.996630250297832, "test_recon_loss": 13.196293275327616}, {"model_id": 409, "latent_dim": 53, "beta": 81.8182, "compression_ratio": 184.70379315869633, "bits_per_pixel": 0.04331259181627337, "compressed_bits": 33, "mse": 0.003219231894742756, "ssim": 0.757713974988291, "psnr": 29.687638668120417, "train_recon_loss": 3.219231894742756, "test_recon_loss": 3.5411550842170314}, {"model_id": 410, "latent_dim": 53, "beta": 82.018, "compression_ratio": 186.75889918279125, "bits_per_pixel": 0.04283597748222942, "compressed_bits": 33, "mse": 0.008017375491897162, "ssim": 0.8074383383504413, "psnr": 26.50152935935494, "train_recon_loss": 8.017375491897162, "test_recon_loss": 8.819113041086878}, {"model_id": 411, "latent_dim": 53, "beta": 82.2178, "compression_ratio": 188.8368713359355, "bits_per_pixel": 0.04236460784063841, "compressed_bits": 33, "mse": 0.005987241736179441, "ssim": 0.7731326088583242, "psnr": 28.20048864026502, "train_recon_loss": 5.987241736179441, "test_recon_loss": 6.585965909797385}, {"model_id": 412, "latent_dim": 53, "beta": 82.4176, "compression_ratio": 190.93796403802358, "bits_per_pixel": 0.041898425178592935, "compressed_bits": 32, "mse": 0.0036268117749652462, "ssim": 0.7326199734760321, "psnr": 27.3042256419441, "train_recon_loss": 3.626811774965246, "test_recon_loss": 3.989492952461771}, {"model_id": 413, "latent_dim": 53, "beta": 82.6174, "compression_ratio": 193.062434539752, "bits_per_pixel": 0.041437372418261835, "compressed_bits": 32, "mse": 0.0026477919969928694, "ssim": 0.7273115170036956, "psnr": 25.056600057327095, "train_recon_loss": 2.6477919969928694, "test_recon_loss": 2.912571196692156}, {"model_id": 414, "latent_dim": 53, "beta": 82.8172, "compression_ratio": 195.21054295411562, "bits_per_pixel": 0.040981393109901884, "compressed_bits": 32, "mse": 0.0024707478472580733, "ssim": 0.7942950044185438, "psnr": 28.20691631571679, "train_recon_loss": 2.4707478472580733, "test_recon_loss": 2.7178226319838807}, {"model_id": 415, "latent_dim": 53, "beta": 83.017, "compression_ratio": 197.38255228825614, "bits_per_pixel": 0.040530431424946084, "compressed_bits": 31, "mse": 0.0037905631317735238, "ssim": 0.7860229940707049, "psnr": 26.645379498733533, "train_recon_loss": 3.7905631317735238, "test_recon_loss": 4.169619444950876}, {"model_id": 416, "latent_dim": 53, "beta": 83.21679999999999, "compression_ratio": 199.57872847566284, "bits_per_pixel": 0.04008443214916835, "compressed_bits": 31, "mse": 0.0042094502088267345, "ssim": 0.7732646125180792, "psnr": 28.790444604494873, "train_recon_loss": 4.209450208826735, "test_recon_loss": 4.630395229709408}, {"model_id": 417, "latent_dim": 54, "beta": 83.4166, "compression_ratio": 201.7993404087331, "bits_per_pixel": 0.0396433406759232, "compressed_bits": 31, "mse": 0.01743965068971217, "ssim": 0.7772400763903755, "psnr": 26.376400103577904, "train_recon_loss": 17.43965068971217, "test_recon_loss": 19.183615758683388}, {"model_id": 418, "latent_dim": 54, "beta": 83.6164, "compression_ratio": 204.04465997169456, "bits_per_pixel": 0.0392071029994599, "compressed_bits": 30, "mse": 0.013131149964875983, "ssim": 0.7633967808052858, "psnr": 27.089579390865232, "train_recon_loss": 13.131149964875982, "test_recon_loss": 14.444264961363581}, {"model_id": 419, "latent_dim": 54, "beta": 83.81620000000001, "compression_ratio": 206.31496207389307, "bits_per_pixel": 0.03877566570831032, "compressed_bits": 30, "mse": 0.003129686731598285, "ssim": 0.7829484870259599, "psnr": 26.250487380337383, "train_recon_loss": 3.129686731598285, "test_recon_loss": 3.4426554047581135}, {"model_id": 420, "latent_dim": 54, "beta": 84.01599999999999, "compression_ratio": 208.6105246834529, "bits_per_pixel": 0.03834897597874919, "compressed_bits": 30, "mse": 0.010259752649549942, "ssim": 0.7978599751962644, "psnr": 27.806286132068628, "train_recon_loss": 10.259752649549942, "test_recon_loss": 11.285727914504935}, {"model_id": 421, "latent_dim": 54, "beta": 84.2158, "compression_ratio": 210.93162886130926, "bits_per_pixel": 0.037926981568326676, "compressed_bits": 29, "mse": 0.011753699041914102, "ssim": 0.7865639641698486, "psnr": 26.38916904564582, "train_recon_loss": 11.753699041914102, "test_recon_loss": 12.929068946105511}, {"model_id": 422, "latent_dim": 54, "beta": 84.4156, "compression_ratio": 213.2785587956208, "bits_per_pixel": 0.037509630809471985, "compressed_bits": 29, "mse": 0.015351698428916424, "ssim": 0.7718523328873156, "psnr": 28.322430493782523, "train_recon_loss": 15.351698428916423, "test_recon_loss": 16.886868271808066}, {"model_id": 423, "latent_dim": 54, "beta": 84.61540000000001, "compression_ratio": 215.65160183656457, "bits_per_pixel": 0.037096872603167325, "compressed_bits": 29, "mse": 0.014494527643324975, "ssim": 0.7450383852710569, "psnr": 27.30592406368249, "train_recon_loss": 14.494527643324975, "test_recon_loss": 15.943980407657472}, {"model_id": 424, "latent_dim": 54, "beta": 84.8152, "compression_ratio": 218.0510485315185, "bits_per_pixel": 0.03668865641269149, "compressed_bits": 28, "mse": 0.01850390179313708, "ssim": 0.7688861666580589, "psnr": 26.452810605025544, "train_recon_loss": 18.50390179313708, "test_recon_loss": 20.35429197245079}, {"model_id": 425, "latent_dim": 55, "beta": 85.015, "compression_ratio": 220.477192660634, "bits_per_pixel": 0.036284932257432505, "compressed_bits": 28, "mse": 0.011683023101271508, "ssim": 0.761264318612738, "psnr": 26.582449015277792, "train_recon_loss": 11.683023101271507, "test_recon_loss": 12.851325411398658}, {"model_id": 426, "latent_dim": 55, "beta": 85.2148, "compression_ratio": 222.93033127280694, "bits_per_pixel": 0.03588565070676787, "compressed_bits": 28, "mse": 0.012900724782769012, "ssim": 0.7975909176022931, "psnr": 26.59488152044662, "train_recon_loss": 12.90072478276901, "test_recon_loss": 14.190797261045914}, {"model_id": 427, "latent_dim": 55, "beta": 85.41460000000001, "compression_ratio": 225.41076472204634, "bits_per_pixel": 0.03549076287401264, "compressed_bits": 27, "mse": 0.01621031487645023, "ssim": 0.7964524921629961, "psnr": 25.793531428538415, "train_recon_loss": 16.21031487645023, "test_recon_loss": 17.83134636409525}, {"model_id": 428, "latent_dim": 55, "beta": 85.6144, "compression_ratio": 227.9187967042488, "bits_per_pixel": 0.03510022041043386, "compressed_bits": 27, "mse": 0.014678697775917349, "ssim": 0.7712164755870476, "psnr": 26.63518478915579, "train_recon_loss": 14.67869777591735, "test_recon_loss": 16.146567553509083}, {"model_id": 429, "latent_dim": 55, "beta": 85.8142, "compression_ratio": 230.45473429438223, "bits_per_pixel": 0.03471397549933091, "compressed_bits": 27, "mse": 0.021400579284164924, "ssim": 0.7491402145681856, "psnr": 26.901327558176238, "train_recon_loss": 21.400579284164923, "test_recon_loss": 23.540637212581416}, {"model_id": 430, "latent_dim": 55, "beta": 86.014, "compression_ratio": 233.01888798408314, "bits_per_pixel": 0.03433198085018094, "compressed_bits": 26, "mse": 0.014950307807314834, "ssim": 0.7483459215862774, "psnr": 26.963068474866944, "train_recon_loss": 14.950307807314834, "test_recon_loss": 16.44533858804632}, {"model_id": 431, "latent_dim": 55, "beta": 86.21379999999999, "compression_ratio": 235.61157171967128, "bits_per_pixel": 0.033954189692848934, "compressed_bits": 26, "mse": 0.011979260670940156, "ssim": 0.7422017092804706, "psnr": 26.78590571476633, "train_recon_loss": 11.979260670940157, "test_recon_loss": 13.177186738034171}, {"model_id": 432, "latent_dim": 55, "beta": 86.4136, "compression_ratio": 238.23310294058965, "bits_per_pixel": 0.033580555771861115, "compressed_bits": 26, "mse": 0.007340282888247257, "ssim": 0.74842945305311, "psnr": 26.709611103169735, "train_recon_loss": 7.340282888247257, "test_recon_loss": 8.074311177071984}, {"model_id": 433, "latent_dim": 55, "beta": 86.6134, "compression_ratio": 240.88380261826964, "bits_per_pixel": 0.03321103334074172, "compressed_bits": 26, "mse": 0.011791869614904103, "ssim": 0.7318148293727381, "psnr": 25.634218790374003, "train_recon_loss": 11.791869614904103, "test_recon_loss": 12.971056576394513}, {"model_id": 434, "latent_dim": 56, "beta": 86.81320000000001, "compression_ratio": 243.56399529543012, "bits_per_pixel": 0.03284557715641192, "compressed_bits": 25, "mse": 0.008715613678625406, "ssim": 0.7320615589500924, "psnr": 27.167147166232336, "train_recon_loss": 8.715613678625406, "test_recon_loss": 9.587175046487946}, {"model_id": 435, "latent_dim": 56, "beta": 87.01299999999999, "compression_ratio": 246.2740091258131, "bits_per_pixel": 0.03248414247365043, "compressed_bits": 25, "mse": 0.02314400561398041, "ssim": 0.7280770310674866, "psnr": 25.346522734196636, "train_recon_loss": 23.14400561398041, "test_recon_loss": 25.458406175378453}, {"model_id": 436, "latent_dim": 56, "beta": 87.2128, "compression_ratio": 249.01417591436226, "bits_per_pixel": 0.032126685039615, "compressed_bits": 25, "mse": 0.025103922380273563, "ssim": 0.7379521832816145, "psnr": 25.91455037749638, "train_recon_loss": 25.103922380273563, "test_recon_loss": 27.61431461830092}, {"model_id": 437, "latent_dim": 56, "beta": 87.4126, "compression_ratio": 251.78483115784687, "bits_per_pixel": 0.03177316108842437, "compressed_bits": 24, "mse": 0.02337128019120944, "ssim": 0.7716426380852847, "psnr": 24.70671302893516, "train_recon_loss": 23.37128019120944, "test_recon_loss": 25.708408210330383}, {"model_id": 438, "latent_dim": 56, "beta": 87.61240000000001, "compression_ratio": 254.58631408594044, "bits_per_pixel": 0.031423527335799555, "compressed_bits": 24, "mse": 0.012039217028269125, "ssim": 0.722419220228689, "psnr": 24.94045471628392, "train_recon_loss": 12.039217028269125, "test_recon_loss": 13.243138731096037}, {"model_id": 439, "latent_dim": 56, "beta": 87.8122, "compression_ratio": 257.41896770275383, "bits_per_pixel": 0.03107774097376437, "compressed_bits": 24, "mse": 0.007729709702422518, "ssim": 0.7385262324543519, "psnr": 26.008442353903067, "train_recon_loss": 7.729709702422518, "test_recon_loss": 8.50268067266477}, {"model_id": 440, "latent_dim": 56, "beta": 88.012, "compression_ratio": 260.28313882883185, "bits_per_pixel": 0.030735759665404153, "compressed_bits": 24, "mse": 0.005624061250469551, "ssim": 0.7568487045739194, "psnr": 25.68191350615347, "train_recon_loss": 5.624061250469551, "test_recon_loss": 6.186467375516506}, {"model_id": 441, "latent_dim": 56, "beta": 88.2118, "compression_ratio": 263.1791781436167, "bits_per_pixel": 0.03039754153968216, "compressed_bits": 23, "mse": 0.015537223810181671, "ssim": 0.737822448605317, "psnr": 23.612195399220383, "train_recon_loss": 15.53722381018167, "test_recon_loss": 17.090946191199837}, {"model_id": 442, "latent_dim": 57, "beta": 88.4116, "compression_ratio": 266.1074402283842, "bits_per_pixel": 0.030063045186313003, "compressed_bits": 23, "mse": 0.014886758729580021, "ssim": 0.7104328530516881, "psnr": 26.91441023540879, "train_recon_loss": 14.886758729580022, "test_recon_loss": 16.375434602538025}, {"model_id": 443, "latent_dim": 57, "beta": 88.6114, "compression_ratio": 269.0682836096566, "bits_per_pixel": 0.02973222965069261, "compressed_bits": 23, "mse": 0.017641742581280387, "ssim": 0.7414220690533471, "psnr": 25.245280347742984, "train_recon_loss": 17.641742581280386, "test_recon_loss": 19.405916839408427}, {"model_id": 444, "latent_dim": 57, "beta": 88.8112, "compression_ratio": 272.0620708031009, "bits_per_pixel": 0.029405054428883727, "compressed_bits": 23, "mse": 0.02128894010424659, "ssim": 0.7204714156767053, "psnr": 23.928079494758403, "train_recon_loss": 21.28894010424659, "test_recon_loss": 23.417834114671248}, {"model_id": 445, "latent_dim": 57, "beta": 89.011, "compression_ratio": 275.08916835791285, "bits_per_pixel": 0.029081479462656868, "compressed_bits": 22, "mse": 0.010594117661298919, "ssim": 0.6973148931461657, "psnr": 23.183754488584263, "train_recon_loss": 10.594117661298919, "test_recon_loss": 11.653529427428811}, {"model_id": 446, "latent_dim": 57, "beta": 89.21079999999999, "compression_ratio": 278.1499469016966, "bits_per_pixel": 0.028761465134585665, "compressed_bits": 22, "mse": 0.015759020012151247, "ssim": 0.6747161459123663, "psnr": 23.628454428293036, "train_recon_loss": 15.759020012151247, "test_recon_loss": 17.334922013366374}, {"model_id": 447, "latent_dim": 57, "beta": 89.4106, "compression_ratio": 281.24478118584267, "bits_per_pixel": 0.028444972263196273, "compressed_bits": 22, "mse": 0.022004094107037447, "ssim": 0.7224240233772827, "psnr": 25.91089565446877, "train_recon_loss": 22.004094107037446, "test_recon_loss": 24.20450351774119}, {"model_id": 448, "latent_dim": 57, "beta": 89.6104, "compression_ratio": 284.3740501314118, "bits_per_pixel": 0.028131962098170097, "compressed_bits": 22, "mse": 0.018662692416128585, "ssim": 0.7180446106574762, "psnr": 25.11293241690945, "train_recon_loss": 18.662692416128586, "test_recon_loss": 20.528961657741444}, {"model_id": 449, "latent_dim": 57, "beta": 89.81020000000001, "compression_ratio": 287.53813687552764, "bits_per_pixel": 0.027822396315599414, "compressed_bits": 21, "mse": 0.01176633557637714, "ssim": 0.7367616355470401, "psnr": 25.202218368915148, "train_recon_loss": 11.76633557637714, "test_recon_loss": 12.942969134014854}, {"model_id": 450, "latent_dim": 58, "beta": 90.00999999999999, "compression_ratio": 290.73742881828815, "bits_per_pixel": 0.027516237013294996, "compressed_bits": 21, "mse": 0.019437889808509627, "ssim": 0.735295391980358, "psnr": 25.75378550532383, "train_recon_loss": 19.437889808509627, "test_recon_loss": 21.38167878936059}, {"model_id": 451, "latent_dim": 58, "beta": 90.2098, "compression_ratio": 293.97231767019696, "bits_per_pixel": 0.027213446706145568, "compressed_bits": 21, "mse": 0.009507849019484763, "ssim": 0.7077059594922746, "psnr": 25.616287786564715, "train_recon_loss": 9.507849019484762, "test_recon_loss": 10.45863392143324}, {"model_id": 452, "latent_dim": 58, "beta": 90.4096, "compression_ratio": 297.24319950012284, "bits_per_pixel": 0.02691398832152826, "compressed_bits": 21, "mse": 0.024375026646070164, "ssim": 0.6994771743365584, "psnr": 23.738453537771193, "train_recon_loss": 24.375026646070165, "test_recon_loss": 26.81252931067718}, {"model_id": 453, "latent_dim": 58, "beta": 90.60940000000001, "compression_ratio": 300.5504747837934, "bits_per_pixel": 0.02661782519476953, "compressed_bits": 20, "mse": 0.016315148807307774, "ssim": 0.6830485349124744, "psnr": 24.458846924998692, "train_recon_loss": 16.315148807307775, "test_recon_loss": 17.94666368803855}, {"model_id": 454, "latent_dim": 58, "beta": 90.8092, "compression_ratio": 303.89454845282773, "bits_per_pixel": 0.026324921064656105, "compressed_bits": 20, "mse": 0.015142275822862546, "ssim": 0.7221520177136038, "psnr": 24.572293193700226, "train_recon_loss": 15.142275822862546, "test_recon_loss": 16.6565034051488}, {"model_id": 455, "latent_dim": 58, "beta": 91.009, "compression_ratio": 307.27582994431543, "bits_per_pixel": 0.026035240068995213, "compressed_bits": 20, "mse": 0.026427469955353562, "ssim": 0.7333696708146431, "psnr": 24.245508115564146, "train_recon_loss": 26.427469955353562, "test_recon_loss": 29.07021695088892}, {"model_id": 456, "latent_dim": 58, "beta": 91.2088, "compression_ratio": 310.6947332509452, "bits_per_pixel": 0.025748746740223868, "compressed_bits": 20, "mse": 0.019000570852913058, "ssim": 0.7262805977570248, "psnr": 26.351918917239992, "train_recon_loss": 19.000570852913057, "test_recon_loss": 20.900627938204362}, {"model_id": 457, "latent_dim": 58, "beta": 91.4086, "compression_ratio": 314.15167697169477, "bits_per_pixel": 0.02546540600106618, "compressed_bits": 19, "mse": 0.021576884468250145, "ssim": 0.7400956851963398, "psnr": 21.833464308194372, "train_recon_loss": 21.576884468250146, "test_recon_loss": 23.73457291507516}, {"model_id": 458, "latent_dim": 58, "beta": 91.6084, "compression_ratio": 317.6470843630814, "bits_per_pixel": 0.025185183160238704, "compressed_bits": 19, "mse": 0.01771545920135901, "ssim": 0.6692640476556644, "psnr": 22.872222246659824, "train_recon_loss": 17.71545920135901, "test_recon_loss": 19.48700512149491}, {"model_id": 459, "latent_dim": 59, "beta": 91.8082, "compression_ratio": 321.18138339098425, "bits_per_pixel": 0.024908043908202945, "compressed_bits": 19, "mse": 0.02659194515218766, "ssim": 0.685164359056, "psnr": 24.602246536790663, "train_recon_loss": 26.59194515218766, "test_recon_loss": 29.251139667406427}, {"model_id": 460, "latent_dim": 59, "beta": 92.008, "compression_ratio": 324.7550067830433, "bits_per_pixel": 0.024633954312964605, "compressed_bits": 19, "mse": 0.018944577819343538, "ssim": 0.7216827778732446, "psnr": 24.458435070268624, "train_recon_loss": 18.94457781934354, "test_recon_loss": 20.839035601277892}, {"model_id": 461, "latent_dim": 59, "beta": 92.20779999999999, "compression_ratio": 328.3683920816411, "bits_per_pixel": 0.024362880815919054, "compressed_bits": 19, "mse": 0.016391341433953713, "ssim": 0.6980102205489203, "psnr": 21.94769230326583, "train_recon_loss": 16.391341433953713, "test_recon_loss": 18.030475577349083}, {"model_id": 462, "latent_dim": 59, "beta": 92.4076, "compression_ratio": 332.0219816974728, "bits_per_pixel": 0.024094790227742597, "compressed_bits": 18, "mse": 0.015097759585366502, "ssim": 0.7133552049340184, "psnr": 24.55936022736735, "train_recon_loss": 15.097759585366502, "test_recon_loss": 16.607535543903154}, {"model_id": 463, "latent_dim": 59, "beta": 92.6074, "compression_ratio": 335.7162229637155, "bits_per_pixel": 0.023829649724328772, "compressed_bits": 18, "mse": 0.024575382002513267, "ssim": 0.720959104559592, "psnr": 22.949888285570953, "train_recon_loss": 24.575382002513265, "test_recon_loss": 27.032920202764593}, {"model_id": 464, "latent_dim": 59, "beta": 92.80720000000001, "compression_ratio": 339.4515681907967, "bits_per_pixel": 0.023567426842769548, "compressed_bits": 18, "mse": 0.022224970126044144, "ssim": 0.7117057174480517, "psnr": 23.97568514933736, "train_recon_loss": 22.224970126044145, "test_recon_loss": 24.44746713864856}, {"model_id": 465, "latent_dim": 59, "beta": 93.00699999999999, "compression_ratio": 343.2284747217741, "bits_per_pixel": 0.02330808947738067, "compressed_bits": 18, "mse": 0.018244799028555705, "ssim": 0.715158475651183, "psnr": 24.55081511160988, "train_recon_loss": 18.244799028555704, "test_recon_loss": 20.069278931411276}, {"model_id": 466, "latent_dim": 59, "beta": 93.2068, "compression_ratio": 347.0474049883311, "bits_per_pixel": 0.023051605875770736, "compressed_bits": 18, "mse": 0.029029621384511137, "ssim": 0.7209828669068105, "psnr": 23.066814385944102, "train_recon_loss": 29.029621384511138, "test_recon_loss": 31.93258352296225}, {"model_id": 467, "latent_dim": 60, "beta": 93.4066, "compression_ratio": 350.9088265673958, "bits_per_pixel": 0.02279794463495353, "compressed_bits": 17, "mse": 0.029325985636930875, "ssim": 0.6791794909242732, "psnr": 23.23880119958847, "train_recon_loss": 29.325985636930874, "test_recon_loss": 32.258584200623964}, {"model_id": 468, "latent_dim": 60, "beta": 93.60640000000001, "compression_ratio": 354.8132122383881, "bits_per_pixel": 0.022547074697503222, "compressed_bits": 17, "mse": 0.033980829559038674, "ssim": 0.6999726218586417, "psnr": 22.815420212267632, "train_recon_loss": 33.980829559038675, "test_recon_loss": 37.37891251494254}, {"model_id": 469, "latent_dim": 60, "beta": 93.8062, "compression_ratio": 358.76104004110715, "bits_per_pixel": 0.022298965347751675, "compressed_bits": 17, "mse": 0.023647307227857645, "ssim": 0.7106789008674543, "psnr": 24.01559757854073, "train_recon_loss": 23.647307227857645, "test_recon_loss": 26.01203795064341}, {"model_id": 470, "latent_dim": 60, "beta": 94.006, "compression_ratio": 362.75279333426, "bits_per_pixel": 0.022053586208027814, "compressed_bits": 17, "mse": 0.024652255271615433, "ssim": 0.6980476190237531, "psnr": 24.165764776619802, "train_recon_loss": 24.652255271615434, "test_recon_loss": 27.117480798776977}, {"model_id": 471, "latent_dim": 60, "beta": 94.2058, "compression_ratio": 366.788960854642, "bits_per_pixel": 0.021810907234938264, "compressed_bits": 17, "mse": 0.02606985339416175, "ssim": 0.6837230895101472, "psnr": 23.405561877171067, "train_recon_loss": 26.06985339416175, "test_recon_loss": 28.676838733577924}, {"model_id": 472, "latent_dim": 60, "beta": 94.4056, "compression_ratio": 370.870036776977, "bits_per_pixel": 0.021570898715688933, "compressed_bits": 16, "mse": 0.023888525837519933, "ssim": 0.7028666904609927, "psnr": 20.682422882156235, "train_recon_loss": 23.888525837519932, "test_recon_loss": 26.277378421271926}, {"model_id": 473, "latent_dim": 60, "beta": 94.6054, "compression_ratio": 374.9965207744219, "bits_per_pixel": 0.021333531264447057, "compressed_bits": 16, "mse": 0.0269116442230168, "ssim": 0.705366309372656, "psnr": 21.6818888718248, "train_recon_loss": 26.9116442230168, "test_recon_loss": 29.602808645318483}, {"model_id": 474, "latent_dim": 60, "beta": 94.8052, "compression_ratio": 379.16891807974423, "bits_per_pixel": 0.02109877581874339, "compressed_bits": 16, "mse": 0.033639441236513114, "ssim": 0.6845600029579728, "psnr": 23.94180213247829, "train_recon_loss": 33.639441236513115, "test_recon_loss": 37.00338536016442}, {"model_id": 475, "latent_dim": 61, "beta": 95.005, "compression_ratio": 383.3877395471828, "bits_per_pixel": 0.020866603635913757, "compressed_bits": 16, "mse": 0.028336933527316137, "ssim": 0.6756273385582264, "psnr": 22.90941278329907, "train_recon_loss": 28.336933527316138, "test_recon_loss": 31.17062688004775}, {"model_id": 476, "latent_dim": 61, "beta": 95.20479999999999, "compression_ratio": 387.65350171499375, "bits_per_pixel": 0.02063698628957999, "compressed_bits": 16, "mse": 0.03142520616896679, "ssim": 0.6876381010255976, "psnr": 22.09697299889858, "train_recon_loss": 31.42520616896679, "test_recon_loss": 34.56772678586347}, {"model_id": 477, "latent_dim": 61, "beta": 95.4046, "compression_ratio": 391.9667268686942, "bits_per_pixel": 0.020409895666169485, "compressed_bits": 16, "mse": 0.030688895697022794, "ssim": 0.6945551448053177, "psnr": 21.563495881538937, "train_recon_loss": 30.688895697022794, "test_recon_loss": 33.75778526672507}, {"model_id": 478, "latent_dim": 61, "beta": 95.6044, "compression_ratio": 396.32794310500896, "bits_per_pixel": 0.02018530396147304, "compressed_bits": 15, "mse": 0.02763087376144881, "ssim": 0.6929472025962162, "psnr": 21.784409989954543, "train_recon_loss": 27.63087376144881, "test_recon_loss": 30.393961137593692}, {"model_id": 479, "latent_dim": 61, "beta": 95.80420000000001, "compression_ratio": 400.73768439652923, "bits_per_pixel": 0.01996318367724063, "compressed_bits": 15, "mse": 0.03380860445415465, "ssim": 0.6987958111686673, "psnr": 21.281354980854434, "train_recon_loss": 33.80860445415465, "test_recon_loss": 37.18946489957012}, {"model_id": 480, "latent_dim": 61, "beta": 96.00399999999999, "compression_ratio": 405.19649065708916, "bits_per_pixel": 0.019743507617814644, "compressed_bits": 15, "mse": 0.03205729774078368, "ssim": 0.6698526077882409, "psnr": 22.386350489621073, "train_recon_loss": 32.05729774078368, "test_recon_loss": 35.26302751486205}, {"model_id": 481, "latent_dim": 61, "beta": 96.2038, "compression_ratio": 409.70490780787304, "bits_per_pixel": 0.019526248886800053, "compressed_bits": 15, "mse": 0.039783159517459504, "ssim": 0.6903342379139334, "psnr": 21.784823667597173, "train_recon_loss": 39.7831595174595, "test_recon_loss": 43.76147546920546}, {"model_id": 482, "latent_dim": 61, "beta": 96.4036, "compression_ratio": 414.26348784425477, "bits_per_pixel": 0.019311380883771382, "compressed_bits": 15, "mse": 0.036869560261207884, "ssim": 0.6302141681891467, "psnr": 21.229329543233334, "train_recon_loss": 36.86956026120789, "test_recon_loss": 40.55651628732867}, {"model_id": 483, "latent_dim": 61, "beta": 96.60340000000001, "compression_ratio": 418.87278890338257, "bits_per_pixel": 0.01909887730101581, "compressed_bits": 14, "mse": 0.03416314545790049, "ssim": 0.6476424535373844, "psnr": 21.67501465124813, "train_recon_loss": 34.163145457900484, "test_recon_loss": 37.579460003690535}, {"model_id": 484, "latent_dim": 62, "beta": 96.8032, "compression_ratio": 423.53337533251545, "bits_per_pixel": 0.01888871212031215, "compressed_bits": 14, "mse": 0.03553450806947315, "ssim": 0.719113271322731, "psnr": 20.306282401008275, "train_recon_loss": 35.53450806947315, "test_recon_loss": 39.087958876420466}, {"model_id": 485, "latent_dim": 62, "beta": 97.003, "compression_ratio": 428.2458177581203, "bits_per_pixel": 0.018680859609745262, "compressed_bits": 14, "mse": 0.03945523671187595, "ssim": 0.6557504410646697, "psnr": 22.088049651311795, "train_recon_loss": 39.455236711875955, "test_recon_loss": 43.40076038306355}, {"model_id": 486, "latent_dim": 62, "beta": 97.2028, "compression_ratio": 433.01069315573596, "bits_per_pixel": 0.01847529432055557, "compressed_bits": 14, "mse": 0.032266113056444896, "ssim": 0.6364509754263789, "psnr": 19.816798452256528, "train_recon_loss": 32.266113056444894, "test_recon_loss": 35.49272436208938}, {"model_id": 487, "latent_dim": 62, "beta": 97.4026, "compression_ratio": 437.82858492061865, "bits_per_pixel": 0.018271991084023113, "compressed_bits": 14, "mse": 0.04502680347368263, "ssim": 0.6827868966061861, "psnr": 20.944719093124746, "train_recon_loss": 45.026803473682634, "test_recon_loss": 49.529483821050896}, {"model_id": 488, "latent_dim": 62, "beta": 97.6024, "compression_ratio": 442.70008293916896, "bits_per_pixel": 0.018070925008386033, "compressed_bits": 14, "mse": 0.044403674864631995, "ssim": 0.6694906337456991, "psnr": 20.735872883407254, "train_recon_loss": 44.403674864631995, "test_recon_loss": 48.8440423510952}, {"model_id": 489, "latent_dim": 62, "beta": 97.8022, "compression_ratio": 447.6257836611564, "bits_per_pixel": 0.01787207147579291, "compressed_bits": 14, "mse": 0.036068230169840704, "ssim": 0.6366741423069204, "psnr": 20.65720923934251, "train_recon_loss": 36.0682301698407, "test_recon_loss": 39.675053186824776}, {"model_id": 490, "latent_dim": 62, "beta": 98.002, "compression_ratio": 452.60629017274636, "bits_per_pixel": 0.017675406139288603, "compressed_bits": 13, "mse": 0.04631377892466256, "ssim": 0.6609128431006405, "psnr": 22.014417745343167, "train_recon_loss": 46.313778924662564, "test_recon_loss": 50.94515681712882}, {"model_id": 491, "latent_dim": 62, "beta": 98.20179999999999, "compression_ratio": 457.64221227034056, "bits_per_pixel": 0.01748090491983332, "compressed_bits": 13, "mse": 0.03298503307934225, "ssim": 0.6231675089063957, "psnr": 20.79939557383156, "train_recon_loss": 32.98503307934225, "test_recon_loss": 36.28353638727648}, {"model_id": 492, "latent_dim": 63, "beta": 98.4016, "compression_ratio": 462.7341665352366, "bits_per_pixel": 0.017288544003354483, "compressed_bits": 13, "mse": 0.04432082294050712, "ssim": 0.6704861839992164, "psnr": 21.260874473214848, "train_recon_loss": 44.32082294050712, "test_recon_loss": 48.75290523455783}, {"model_id": 493, "latent_dim": 63, "beta": 98.6014, "compression_ratio": 467.88277640912287, "bits_per_pixel": 0.01709829983783095, "compressed_bits": 13, "mse": 0.04424807208760738, "ssim": 0.6494021474093405, "psnr": 20.676042406119535, "train_recon_loss": 44.248072087607376, "test_recon_loss": 48.672879296368116}, {"model_id": 494, "latent_dim": 63, "beta": 98.80120000000001, "compression_ratio": 473.0886722704087, "bits_per_pixel": 0.016910149130409422, "compressed_bits": 13, "mse": 0.05506242769070566, "ssim": 0.6956979975092513, "psnr": 19.943725489456135, "train_recon_loss": 55.06242769070566, "test_recon_loss": 60.56867045977623}, {"model_id": 495, "latent_dim": 63, "beta": 99.00099999999999, "compression_ratio": 478.3524915114063, "bits_per_pixel": 0.016724068844552555, "compressed_bits": 13, "mse": 0.055539520766755945, "ssim": 0.6169443682062054, "psnr": 20.104245791808687, "train_recon_loss": 55.53952076675594, "test_recon_loss": 61.09347284343154}, {"model_id": 496, "latent_dim": 63, "beta": 99.2008, "compression_ratio": 483.67487861637096, "bits_per_pixel": 0.016540036197218416, "compressed_bits": 12, "mse": 0.046039677158717186, "ssim": 0.6208659227381188, "psnr": 20.798675566543672, "train_recon_loss": 46.03967715871719, "test_recon_loss": 50.64364487458891}, {"model_id": 497, "latent_dim": 63, "beta": 99.4006, "compression_ratio": 489.05648524041, "bits_per_pixel": 0.01635802865607102, "compressed_bits": 12, "mse": 0.048888252443382844, "ssim": 0.6645073532972553, "psnr": 21.526709804825323, "train_recon_loss": 48.88825244338285, "test_recon_loss": 53.77707768772113}, {"model_id": 498, "latent_dim": 63, "beta": 99.60040000000001, "compression_ratio": 494.4979702892672, "bits_per_pixel": 0.01617802393672158, "compressed_bits": 12, "mse": 0.04343297256845839, "ssim": 0.6388155557619082, "psnr": 19.31544752234067, "train_recon_loss": 43.43297256845839, "test_recon_loss": 47.77626982530423}, {"model_id": 499, "latent_dim": 63, "beta": 99.8002, "compression_ratio": 499.99999999999994, "bits_per_pixel": 0.016, "compressed_bits": 12, "mse": 0.056715780240037816, "ssim": 0.6581392122031451, "psnr": 21.414204610675313, "train_recon_loss": 56.715780240037816, "test_recon_loss": 62.387358264041595}]