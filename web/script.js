// Global variables
let curveData = [];
let reconstructionData = [];
let sampleImages = [];
let metadata = {};
let fittedCurves = {};
let currentMetric = 'ssim';
let selectedModel = null;
let selectedImageIndex = 0;
let showCurve = true;

// Chart dimensions and margins
const margin = { top: 20, right: 80, bottom: 60, left: 80 };
const width = 800 - margin.left - margin.right;
const height = 400 - margin.bottom - margin.top;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    loadData();
    setupEventListeners();
});

// Load all data files
async function loadData() {
    try {
        showLoading(true);
        
        // Load all data files
        const [curveResponse, reconstructionResponse, sampleResponse, metadataResponse, curvesResponse] = await Promise.all([
            fetch('distortion_curves.json'),
            fetch('reconstructions.json'),
            fetch('sample_images.json'),
            fetch('metadata.json'),
            fetch('fitted_curves.json')
        ]);

        curveData = await curveResponse.json();
        reconstructionData = await reconstructionResponse.json();
        sampleImages = await sampleResponse.json();
        metadata = await metadataResponse.json();
        fittedCurves = await curvesResponse.json();
        
        console.log('Data loaded:', {
            curves: curveData.length,
            reconstructions: reconstructionData.length,
            samples: sampleImages.images.length
        });
        
        // Initialize the interface
        initializeInterface();
        
    } catch (error) {
        console.error('Error loading data:', error);
        alert('Error loading data. Please check that all JSON files are present.');
    } finally {
        showLoading(false);
    }
}

// Initialize the interface with loaded data
function initializeInterface() {
    populateImageSelect();
    populateModelSelect();
    updateStatistics();
    createDistortionChart();
    updateImageDisplay();
}

// Setup event listeners
function setupEventListeners() {
    // Metric selection
    document.getElementById('metric-select').addEventListener('change', function() {
        currentMetric = this.value;
        updateDistortionChart();
    });
    
    // Compression range sliders
    document.getElementById('compression-min').addEventListener('input', updateCompressionRange);
    document.getElementById('compression-max').addEventListener('input', updateCompressionRange);

    // Show curve checkbox
    document.getElementById('show-curve').addEventListener('change', function() {
        showCurve = this.checked;
        updateDistortionChart();
    });
    
    // Image and model selection
    document.getElementById('image-select').addEventListener('change', function() {
        selectedImageIndex = parseInt(this.value);
        updateImageDisplay();
    });
    
    document.getElementById('model-select').addEventListener('change', function() {
        selectedModel = reconstructionData[parseInt(this.value)];
        updateImageDisplay();
    });
    
    // Buttons
    document.getElementById('reset-zoom').addEventListener('click', resetChartZoom);
    document.getElementById('export-data').addEventListener('click', exportData);
}

// Populate image selection dropdown
function populateImageSelect() {
    const select = document.getElementById('image-select');
    select.innerHTML = '';
    
    for (let i = 0; i < Math.min(20, sampleImages.images.length); i++) {
        const option = document.createElement('option');
        option.value = i;
        option.textContent = `Image ${i + 1} (Digit ${sampleImages.labels[i]})`;
        select.appendChild(option);
    }
}

// Populate model selection dropdown
function populateModelSelect() {
    const select = document.getElementById('model-select');
    select.innerHTML = '';
    
    reconstructionData.forEach((model, index) => {
        const option = document.createElement('option');
        option.value = index;
        option.textContent = `${model.compression_ratio.toFixed(1)}x compression (β=${model.beta.toFixed(2)})`;
        select.appendChild(option);
    });
    
    // Select the first model by default
    if (reconstructionData.length > 0) {
        selectedModel = reconstructionData[0];
    }
}

// Update statistics display
function updateStatistics() {
    document.getElementById('models-count').textContent = curveData.length;
    
    const compressionRatios = curveData.map(d => d.compression_ratio);
    const minComp = Math.min(...compressionRatios);
    const maxComp = Math.max(...compressionRatios);
    document.getElementById('compression-range-stat').textContent = `${minComp.toFixed(1)}x - ${maxComp.toFixed(1)}x`;
    
    const ssimValues = curveData.map(d => d.ssim);
    const bestSSIM = Math.max(...ssimValues);
    document.getElementById('best-quality').textContent = bestSSIM.toFixed(3);
    
    document.getElementById('best-compression').textContent = `${maxComp.toFixed(1)}x`;
}

// Create the distortion chart
function createDistortionChart() {
    const svg = d3.select('#distortion-chart')
        .append('svg')
        .attr('width', width + margin.left + margin.right)
        .attr('height', height + margin.top + margin.bottom);
    
    const g = svg.append('g')
        .attr('transform', `translate(${margin.left},${margin.top})`);
    
    // Add chart elements
    g.append('g').attr('class', 'x-axis');
    g.append('g').attr('class', 'y-axis');
    g.append('path').attr('class', 'fitted-curve');
    g.append('g').attr('class', 'points');
    
    // Add axis labels
    g.append('text')
        .attr('class', 'x-label')
        .attr('text-anchor', 'middle')
        .attr('x', width / 2)
        .attr('y', height + 40)
        .text('Compression Ratio');
    
    g.append('text')
        .attr('class', 'y-label')
        .attr('text-anchor', 'middle')
        .attr('transform', 'rotate(-90)')
        .attr('x', -height / 2)
        .attr('y', -50)
        .text('Quality Metric');
    
    updateDistortionChart();
}

// Update the distortion chart
function updateDistortionChart() {
    const svg = d3.select('#distortion-chart svg g');
    
    // Get current compression range
    const minComp = parseFloat(document.getElementById('compression-min').value);
    const maxComp = parseFloat(document.getElementById('compression-max').value);
    
    // Filter data based on compression range
    const filteredData = curveData.filter(d => 
        d.compression_ratio >= minComp && d.compression_ratio <= maxComp
    );
    
    // Set up scales
    const xScale = d3.scaleLinear()
        .domain(d3.extent(filteredData, d => d.compression_ratio))
        .range([0, width]);
    
    const yScale = d3.scaleLinear()
        .domain(d3.extent(filteredData, d => d[currentMetric]))
        .range([height, 0]);
    
    // Color scale based on beta values
    const colorScale = d3.scaleSequential(d3.interpolateViridis)
        .domain(d3.extent(filteredData, d => Math.log(d.beta)));
    
    // Update axes
    svg.select('.x-axis')
        .attr('transform', `translate(0,${height})`)
        .call(d3.axisBottom(xScale));
    
    svg.select('.y-axis')
        .call(d3.axisLeft(yScale));
    
    // Update y-axis label
    const metricLabels = {
        'mse': 'Mean Squared Error',
        'ssim': 'Structural Similarity (SSIM)',
        'psnr': 'Peak Signal-to-Noise Ratio (dB)'
    };
    svg.select('.y-label').text(metricLabels[currentMetric]);

    // Update fitted curve
    if (showCurve && fittedCurves[currentMetric]) {
        const curveData = fittedCurves[currentMetric];
        const line = d3.line()
            .x(d => xScale(d.x))
            .y(d => yScale(d.y))
            .curve(d3.curveCardinal);

        const curvePoints = curveData.smooth_x.map((x, i) => ({
            x: x,
            y: curveData.smooth_y[i]
        })).filter(d => d.x >= minComp && d.x <= maxComp);

        svg.select('.fitted-curve')
            .datum(curvePoints)
            .attr('d', line)
            .attr('fill', 'none')
            .attr('stroke', '#ff6b6b')
            .attr('stroke-width', 3)
            .attr('opacity', 0.8);
    } else {
        svg.select('.fitted-curve').attr('d', null);
    }
    
    // Update points
    const points = svg.select('.points')
        .selectAll('.point')
        .data(filteredData, d => d.model_id);
    
    points.exit().remove();
    
    const pointsEnter = points.enter()
        .append('circle')
        .attr('class', 'point')
        .attr('r', 0);
    
    points.merge(pointsEnter)
        .transition()
        .duration(500)
        .attr('cx', d => xScale(d.compression_ratio))
        .attr('cy', d => yScale(d[currentMetric]))
        .attr('r', 6)
        .attr('fill', d => colorScale(Math.log(d.beta)))
        .attr('stroke', '#fff')
        .attr('stroke-width', 2);
    
    // Add hover effects
    svg.selectAll('.point')
        .on('mouseover', function(_, d) {
            d3.select(this)
                .transition()
                .duration(200)
                .attr('r', 8);
            
            showPointInfo(d);
        })
        .on('mouseout', function() {
            d3.select(this)
                .transition()
                .duration(200)
                .attr('r', 6);
            
            hidePointInfo();
        })
        .on('click', function(_, d) {
            selectModelFromChart(d);
        });
}

// Show point information
function showPointInfo(d) {
    const info = document.getElementById('point-info');
    info.innerHTML = `
        <strong>Model ${d.model_id}</strong><br>
        Compression: ${d.compression_ratio.toFixed(2)}x<br>
        β: ${d.beta.toFixed(3)}<br>
        Latent Dim: ${d.latent_dim}<br>
        MSE: ${d.mse.toFixed(6)}<br>
        SSIM: ${d.ssim.toFixed(4)}<br>
        PSNR: ${d.psnr.toFixed(2)} dB
    `;
    info.style.display = 'block';
}

// Hide point information
function hidePointInfo() {
    document.getElementById('point-info').style.display = 'none';
}

// Select model from chart click
function selectModelFromChart(d) {
    // Find corresponding reconstruction data
    const modelIndex = reconstructionData.findIndex(r => r.model_id === d.model_id);
    if (modelIndex !== -1) {
        document.getElementById('model-select').value = modelIndex;
        selectedModel = reconstructionData[modelIndex];
        updateImageDisplay();
    }
}

// Update compression range display
function updateCompressionRange() {
    const minVal = document.getElementById('compression-min').value;
    const maxVal = document.getElementById('compression-max').value;
    
    // Ensure min <= max
    if (parseFloat(minVal) > parseFloat(maxVal)) {
        document.getElementById('compression-min').value = maxVal;
    }
    
    document.getElementById('compression-min-value').textContent = `${document.getElementById('compression-min').value}x`;
    document.getElementById('compression-max-value').textContent = `${document.getElementById('compression-max').value}x`;
    
    updateDistortionChart();
}

// Update image display
function updateImageDisplay() {
    if (!selectedModel || !sampleImages.images[selectedImageIndex]) return;
    
    const originalImage = sampleImages.images[selectedImageIndex];
    const reconstructedImage = selectedModel.reconstructed_images[selectedImageIndex];
    const label = sampleImages.labels[selectedImageIndex];
    
    // Draw original image
    drawImageOnCanvas('original-canvas', originalImage);
    
    // Draw reconstructed image
    drawImageOnCanvas('reconstructed-canvas', reconstructedImage);
    
    // Update information
    document.getElementById('image-label').textContent = label;
    document.getElementById('compressed-size').textContent = `${Math.round(6272 / selectedModel.compression_ratio)} bits`;
    document.getElementById('compression-ratio').textContent = `${selectedModel.compression_ratio.toFixed(1)}x`;
    
    const qualityText = currentMetric === 'mse' ? 
        `MSE: ${selectedModel.mse.toFixed(6)}` :
        currentMetric === 'ssim' ?
        `SSIM: ${selectedModel.ssim.toFixed(4)}` :
        `PSNR: ${selectedModel.psnr.toFixed(2)} dB`;
    
    document.getElementById('quality-metric').textContent = qualityText;
}

// Draw image on canvas
function drawImageOnCanvas(canvasId, imageData) {
    const canvas = document.getElementById(canvasId);
    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // Create image data
    const imageDataObj = ctx.createImageData(28, 28);
    
    for (let i = 0; i < 28; i++) {
        for (let j = 0; j < 28; j++) {
            const pixelIndex = (i * 28 + j) * 4;
            const value = Math.round(imageData[i][j] * 255);
            
            imageDataObj.data[pixelIndex] = value;     // R
            imageDataObj.data[pixelIndex + 1] = value; // G
            imageDataObj.data[pixelIndex + 2] = value; // B
            imageDataObj.data[pixelIndex + 3] = 255;   // A
        }
    }
    
    // Draw scaled up
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = 28;
    tempCanvas.height = 28;
    const tempCtx = tempCanvas.getContext('2d');
    tempCtx.putImageData(imageDataObj, 0, 0);
    
    // Scale to canvas size
    ctx.imageSmoothingEnabled = false;
    ctx.drawImage(tempCanvas, 0, 0, 280, 280);
}

// Reset chart zoom
function resetChartZoom() {
    document.getElementById('compression-min').value = 2;
    document.getElementById('compression-max').value = 500;
    updateCompressionRange();
}

// Export data
function exportData() {
    const exportData = {
        metadata: metadata,
        curves: curveData,
        reconstructions: reconstructionData,
        timestamp: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = 'mnist_compression_analysis.json';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Show/hide loading overlay
function showLoading(show) {
    const overlay = document.getElementById('loading-overlay');
    overlay.style.display = show ? 'flex' : 'none';
}
