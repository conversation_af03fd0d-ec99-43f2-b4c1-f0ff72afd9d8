/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header */
.header {
    text-align: center;
    margin-bottom: 40px;
    padding: 30px 0;
    border-bottom: 2px solid #e0e0e0;
}

.header h1 {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
}

.subtitle {
    font-size: 1.2rem;
    color: #666;
    font-weight: 300;
}

/* Main Content */
.main-content {
    display: grid;
    gap: 30px;
}

/* Control Panel */
.control-panel {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 15px;
    border: 1px solid #e9ecef;
}

.control-panel h2 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.control-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.control-group label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

select, .btn {
    padding: 10px 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

select:focus, .btn:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    cursor: pointer;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

/* Range Slider */
.range-slider {
    display: flex;
    gap: 10px;
    align-items: center;
}

.range-slider input[type="range"] {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #dee2e6;
    outline: none;
    -webkit-appearance: none;
}

.range-slider input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
}

.range-values {
    font-weight: 600;
    color: #667eea;
    text-align: center;
    margin-top: 5px;
}

/* Chart Section */
.curve-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.curve-section h2 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.chart-container {
    position: relative;
}

#distortion-chart {
    width: 100%;
    height: 500px;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    background: #fafafa;
}

.chart-info {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.point-info {
    margin-top: 10px;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Image Section */
.image-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.image-section h2 {
    color: #495057;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.image-controls {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.image-comparison {
    display: flex;
    justify-content: center;
}

.image-pair {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    max-width: 800px;
}

.image-container {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.image-container:hover {
    border-color: #667eea;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.1);
}

.image-container h3 {
    margin-bottom: 15px;
    color: #495057;
    font-size: 1.3rem;
}

.image-container canvas {
    border: 2px solid #dee2e6;
    border-radius: 10px;
    background: white;
    margin-bottom: 15px;
    image-rendering: pixelated;
}

.image-info {
    text-align: left;
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.image-info p {
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.image-info span {
    font-weight: 600;
    color: #667eea;
}

/* Statistics Section */
.stats-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.stats-section h2 {
    color: #495057;
    margin-bottom: 25px;
    font-size: 1.8rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    text-align: center;
    padding: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.2);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Details Section */
.details-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
}

.details-section h2 {
    color: #495057;
    margin-bottom: 25px;
    font-size: 1.8rem;
    text-align: center;
}

.architecture-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.arch-card {
    padding: 25px;
    background: #f8f9fa;
    border-radius: 15px;
    border-left: 5px solid #667eea;
}

.arch-card h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.arch-card p {
    margin-bottom: 15px;
    color: #666;
    line-height: 1.6;
}

.arch-card ul {
    list-style: none;
    padding-left: 0;
}

.arch-card li {
    padding: 5px 0;
    color: #555;
    position: relative;
    padding-left: 20px;
}

.arch-card li::before {
    content: "▶";
    position: absolute;
    left: 0;
    color: #667eea;
    font-size: 0.8rem;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px 0;
    border-top: 2px solid #e0e0e0;
    color: #666;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #e9ecef;
    border-top: 5px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-overlay p {
    font-size: 1.1rem;
    color: #495057;
    font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        padding: 15px;
    }
    
    .header h1 {
        font-size: 2rem;
    }
    
    .image-pair {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .controls {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .architecture-info {
        grid-template-columns: 1fr;
    }
}
