# MNIST Compression Analysis with Beta-VAE

A comprehensive analysis of MNIST digit compression using Beta-Variational Autoencoders (β-VAE) with interactive visualization of distortion-complexity curves.

## 🎯 Project Overview

This project implements a complete pipeline for:
- Training multiple β-VAE models with different compression ratios
- Evaluating compression performance using multiple quality metrics
- Creating interactive visualizations of rate-distortion trade-offs
- Displaying image reconstructions at various compression levels

## 🏗️ Architecture

### Machine Learning Components
- **Beta-VAE**: Variational autoencoder with configurable β parameter
- **Encoder**: 3 convolutional layers reducing 28×28 images to latent space
- **Decoder**: 4 transposed convolutional layers reconstructing images
- **Training Pipeline**: Automated training of 50+ models with different configurations

### Web Visualization
- **Interactive Charts**: D3.js-powered distortion-complexity curves
- **Image Comparison**: Side-by-side original vs. reconstructed images
- **Real-time Controls**: Dynamic filtering and metric selection
- **Responsive Design**: Modern CSS with animations and hover effects

## 📁 Project Structure

```
MNIST DIstortion Curve/
├── MNIST/                          # MNIST dataset files
├── web/                            # Web visualization
│   ├── index.html                  # Main HTML page
│   ├── styles.css                  # CSS styling
│   ├── script.js                   # JavaScript functionality
│   ├── distortion_curves.json     # Curve data
│   ├── reconstructions.json       # Reconstruction data
│   ├── sample_images.json         # Sample images
│   └── metadata.json              # Dataset metadata
├── models/                         # Trained model files (generated)
├── mnist_loader.py                 # MNIST data loading utilities
├── beta_vae.py                     # Beta-VAE model implementation
├── train_vae.py                    # Training pipeline
├── compression_eval.py             # Evaluation metrics
├── export_for_web.py              # Web data export
├── test_web.py                     # Web application testing
└── requirements.txt                # Python dependencies
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Train Models (Optional - Mock Data Available)
```bash
# Train 50 models with different compression ratios
python3 train_vae.py

# Evaluate models and create distortion curves
python3 compression_eval.py
```

### 3. Launch Web Application
```bash
# Export data for web visualization
python3 export_for_web.py

# Start web server
cd web
python3 -m http.server 8000

# Open browser to http://localhost:8000
```

### 4. Test Application
```bash
python3 test_web.py
```

## 🔬 Technical Details

### Beta-VAE Implementation
- **Latent Dimensions**: 4-64 (configurable)
- **Beta Values**: 0.1-100 (controls compression vs. quality trade-off)
- **Loss Function**: Reconstruction loss + β × KL divergence
- **Compression Ratios**: 2x to 50x

### Quality Metrics
- **MSE**: Mean Squared Error
- **SSIM**: Structural Similarity Index
- **PSNR**: Peak Signal-to-Noise Ratio

### Web Features
- **Interactive Curves**: Hover for details, click to select models
- **Dynamic Filtering**: Compression ratio range sliders
- **Image Comparison**: Real-time reconstruction viewing
- **Export Functionality**: Download analysis data
- **Responsive Design**: Works on desktop and mobile

## 📊 Results

The analysis generates comprehensive distortion-complexity curves showing:
- Trade-offs between compression ratio and image quality
- Optimal operating points for different use cases
- Visual comparison of reconstruction quality
- Statistical analysis of model performance

### Key Findings
- Higher β values increase compression but reduce quality
- Optimal compression ratios depend on quality requirements
- SSIM provides better perceptual quality assessment than MSE
- Latent dimension significantly affects compression capability

## 🎨 Visualization Features

### Interactive Chart
- **Scatter Plot**: Compression ratio vs. quality metric
- **Color Coding**: Points colored by β parameter
- **Hover Effects**: Detailed model information
- **Zoom/Pan**: Explore specific regions
- **Metric Selection**: Switch between MSE, SSIM, PSNR

### Image Comparison
- **Side-by-Side**: Original vs. reconstructed images
- **Quality Metrics**: Real-time quality assessment
- **Model Selection**: Choose from different compression levels
- **Image Selection**: Browse through sample digits

### Statistics Dashboard
- **Model Count**: Total models trained
- **Compression Range**: Min/max compression ratios
- **Best Quality**: Highest SSIM achieved
- **Best Compression**: Maximum compression ratio

## 🔧 Customization

### Training Parameters
```python
# Modify in train_vae.py
num_epochs = 20
learning_rate = 1e-3
batch_size = 128
num_models = 50
```

### Model Architecture
```python
# Modify in beta_vae.py
latent_dim = 20  # Latent space dimensions
beta = 4.0       # Beta parameter
```

### Web Appearance
```css
/* Modify in web/styles.css */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
}
```

## 📈 Performance

### Training Time
- **Per Model**: ~2-3 minutes (CPU)
- **50 Models**: ~2-3 hours total
- **GPU Acceleration**: Significantly faster with CUDA

### Web Performance
- **Load Time**: <2 seconds
- **Interactivity**: Real-time updates
- **Data Size**: ~5MB total JSON data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **PyTorch**: Deep learning framework
- **D3.js**: Data visualization library
- **MNIST**: Classic computer vision dataset
- **Beta-VAE**: Higgins et al. (2017) paper

## 📞 Support

For questions or issues:
1. Check the documentation
2. Run the test suite
3. Open an issue on GitHub

---

**Built with ❤️ for exploring the fascinating world of neural compression!**
