# MNIST Compression Analysis with Enhanced Beta-VAE

A comprehensive analysis of MNIST digit compression using Enhanced Beta-Variational Autoencoders (β-VAE) with interactive visualization of distortion-complexity curves. **Now supports compression ratios up to 500x with 500 evaluation points and fitted curve visualization.**

## 🎯 Project Overview

This project implements a complete pipeline for:
- Training 500+ enhanced β-VAE models with compression ratios from 2x to 500x
- Ensuring reconstruction fidelity (digits remain recognizable)
- Evaluating compression performance using multiple quality metrics (MSE, SSIM, PSNR, correlation)
- Creating interactive visualizations with fitted distortion-complexity curves
- Displaying high-quality image reconstructions at various compression levels

## 🏗️ Architecture

### Machine Learning Components
- **Enhanced Beta-VAE**: Advanced variational autoencoder with optimized architecture
- **Deep Encoder**: 4 convolutional layers with batch normalization and dropout
- **Deep Decoder**: 5 transposed convolutional layers for high-quality reconstruction
- **Fidelity-Preserving Training**: Optimized β values (0.01-10) to maintain digit recognition
- **Capacity Scheduling**: Progressive training for better convergence
- **Training Pipeline**: Automated training of 500 models with compression ratios up to 500x

### Web Visualization
- **Interactive Charts**: D3.js-powered distortion-complexity curves with 500 data points
- **Fitted Curves**: Smooth spline interpolation overlays for trend visualization
- **Image Comparison**: Side-by-side original vs. reconstructed images
- **Real-time Controls**: Dynamic filtering, metric selection, and curve toggling
- **Extended Range**: Compression ratios from 2x to 500x
- **Quality Metrics**: MSE, SSIM, PSNR, and correlation-based fidelity scores
- **Responsive Design**: Modern CSS with animations and hover effects

## 📁 Project Structure

```
MNIST DIstortion Curve/
├── MNIST/                          # MNIST dataset files
├── web/                            # Web visualization
│   ├── index.html                  # Main HTML page
│   ├── styles.css                  # CSS styling
│   ├── script.js                   # JavaScript functionality
│   ├── distortion_curves.json     # Curve data
│   ├── reconstructions.json       # Reconstruction data
│   ├── sample_images.json         # Sample images
│   └── metadata.json              # Dataset metadata
├── models/                         # Trained model files (generated)
├── mnist_loader.py                 # MNIST data loading utilities
├── beta_vae.py                     # Beta-VAE model implementation
├── train_vae.py                    # Training pipeline
├── compression_eval.py             # Evaluation metrics
├── export_for_web.py              # Web data export
├── test_web.py                     # Web application testing
└── requirements.txt                # Python dependencies
```

## 🚀 Quick Start

### 1. Install Dependencies
```bash
pip install -r requirements.txt
```

### 2. Train Models (Optional - Mock Data Available)
```bash
# Train 50 models with different compression ratios
python3 train_vae.py

# Evaluate models and create distortion curves
python3 compression_eval.py
```

### 3. Launch Web Application
```bash
# Export data for web visualization
python3 export_for_web.py

# Start web server
cd web
python3 -m http.server 8000

# Open browser to http://localhost:8000
```

### 4. Test Application
```bash
python3 test_web.py
```

## 🔬 Technical Details

### Beta-VAE Implementation
- **Latent Dimensions**: 4-64 (configurable)
- **Beta Values**: 0.1-100 (controls compression vs. quality trade-off)
- **Loss Function**: Reconstruction loss + β × KL divergence
- **Compression Ratios**: 2x to 50x

### Quality Metrics
- **MSE**: Mean Squared Error
- **SSIM**: Structural Similarity Index
- **PSNR**: Peak Signal-to-Noise Ratio

### Web Features
- **Interactive Curves**: Hover for details, click to select models
- **Dynamic Filtering**: Compression ratio range sliders
- **Image Comparison**: Real-time reconstruction viewing
- **Export Functionality**: Download analysis data
- **Responsive Design**: Works on desktop and mobile

## 📊 Results

The analysis generates comprehensive distortion-complexity curves showing:
- Trade-offs between compression ratio and image quality
- Optimal operating points for different use cases
- Visual comparison of reconstruction quality
- Statistical analysis of model performance

### Key Findings
- Higher β values increase compression but reduce quality
- Optimal compression ratios depend on quality requirements
- SSIM provides better perceptual quality assessment than MSE
- Latent dimension significantly affects compression capability

## 🎨 Visualization Features

### Interactive Chart
- **Scatter Plot**: Compression ratio vs. quality metric
- **Color Coding**: Points colored by β parameter
- **Hover Effects**: Detailed model information
- **Zoom/Pan**: Explore specific regions
- **Metric Selection**: Switch between MSE, SSIM, PSNR

### Image Comparison
- **Side-by-Side**: Original vs. reconstructed images
- **Quality Metrics**: Real-time quality assessment
- **Model Selection**: Choose from different compression levels
- **Image Selection**: Browse through sample digits

### Statistics Dashboard
- **Model Count**: Total models trained
- **Compression Range**: Min/max compression ratios
- **Best Quality**: Highest SSIM achieved
- **Best Compression**: Maximum compression ratio

## 🔧 Customization

### Training Parameters
```python
# Modify in train_vae.py
num_epochs = 20
learning_rate = 1e-3
batch_size = 128
num_models = 50
```

### Model Architecture
```python
# Modify in beta_vae.py
latent_dim = 20  # Latent space dimensions
beta = 4.0       # Beta parameter
```

### Web Appearance
```css
/* Modify in web/styles.css */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
}
```

## 📈 Performance

### Training Time
- **Per Model**: ~2-3 minutes (CPU)
- **50 Models**: ~2-3 hours total
- **GPU Acceleration**: Significantly faster with CUDA

### Web Performance
- **Load Time**: <2 seconds
- **Interactivity**: Real-time updates
- **Data Size**: ~5MB total JSON data

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is open source and available under the MIT License.

## 🙏 Acknowledgments

- **PyTorch**: Deep learning framework
- **D3.js**: Data visualization library
- **MNIST**: Classic computer vision dataset
- **Beta-VAE**: Higgins et al. (2017) paper

## 📞 Support

For questions or issues:
1. Check the documentation
2. Run the test suite
3. Open an issue on GitHub

---

**Built with ❤️ for exploring the fascinating world of neural compression!**
