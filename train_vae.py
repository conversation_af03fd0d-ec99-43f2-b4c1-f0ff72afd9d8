import torch
import torch.optim as optim
import numpy as np
import os
import json
from tqdm import tqdm
import matplotlib.pyplot as plt

from mnist_loader import load_mnist_data
from beta_vae import BetaVAE

def train_model(model, train_loader, num_epochs=20, learning_rate=1e-3, device='cpu'):
    """Train a single Beta-VAE model."""
    model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    model.train()
    train_losses = []
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        epoch_recon_loss = 0
        epoch_kl_loss = 0
        
        for batch_idx, (data, _) in enumerate(train_loader):
            data = data.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            recon_batch, mu, logvar, _ = model(data)
            
            # Compute loss
            loss, recon_loss, kl_loss = model.loss_function(recon_batch, data, mu, logvar)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            epoch_recon_loss += recon_loss.item()
            epoch_kl_loss += kl_loss.item()
        
        avg_loss = epoch_loss / len(train_loader.dataset)
        avg_recon_loss = epoch_recon_loss / len(train_loader.dataset)
        avg_kl_loss = epoch_kl_loss / len(train_loader.dataset)
        
        train_losses.append({
            'epoch': epoch,
            'total_loss': avg_loss,
            'recon_loss': avg_recon_loss,
            'kl_loss': avg_kl_loss
        })
        
        if epoch % 5 == 0:
            print(f'Epoch {epoch:3d}: Loss={avg_loss:.4f}, Recon={avg_recon_loss:.4f}, KL={avg_kl_loss:.4f}')
    
    return train_losses

def evaluate_model(model, test_loader, device='cpu'):
    """Evaluate model performance on test set."""
    model.eval()
    total_loss = 0
    total_recon_loss = 0
    total_kl_loss = 0
    
    with torch.no_grad():
        for data, _ in test_loader:
            data = data.to(device)
            recon_batch, mu, logvar, _ = model(data)
            loss, recon_loss, kl_loss = model.loss_function(recon_batch, data, mu, logvar)
            
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_loss += kl_loss.item()
    
    avg_loss = total_loss / len(test_loader.dataset)
    avg_recon_loss = total_recon_loss / len(test_loader.dataset)
    avg_kl_loss = total_kl_loss / len(test_loader.dataset)
    
    return {
        'total_loss': avg_loss,
        'recon_loss': avg_recon_loss,
        'kl_loss': avg_kl_loss
    }

def generate_model_configurations(num_models=50):
    """Generate configurations for different compression ratios."""
    configurations = []
    
    # Create a range of latent dimensions and beta values
    latent_dims = np.logspace(np.log10(4), np.log10(64), 8).astype(int)  # 4 to 64
    betas = np.logspace(np.log10(0.1), np.log10(100), 8)  # 0.1 to 100
    
    # Generate all combinations
    for latent_dim in latent_dims:
        for beta in betas:
            configurations.append({
                'latent_dim': int(latent_dim),
                'beta': float(beta)
            })
    
    # If we have more than needed, sample evenly
    if len(configurations) > num_models:
        indices = np.linspace(0, len(configurations)-1, num_models).astype(int)
        configurations = [configurations[i] for i in indices]
    
    return configurations

def train_multiple_models(configurations, train_loader, test_loader, device='cpu', save_dir='models'):
    """Train multiple models with different configurations."""
    os.makedirs(save_dir, exist_ok=True)
    
    results = []
    
    for i, config in enumerate(tqdm(configurations, desc="Training models")):
        print(f"\nTraining model {i+1}/{len(configurations)}")
        print(f"Configuration: latent_dim={config['latent_dim']}, beta={config['beta']:.3f}")
        
        # Create model
        model = BetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        compression_ratio = model.get_compression_ratio()
        
        # Train model
        train_losses = train_model(model, train_loader, num_epochs=15, device=device)
        
        # Evaluate model
        test_metrics = evaluate_model(model, test_loader, device=device)
        
        # Save model
        model_path = os.path.join(save_dir, f'model_{i:03d}.pth')
        torch.save({
            'model_state_dict': model.state_dict(),
            'config': config,
            'train_losses': train_losses,
            'test_metrics': test_metrics
        }, model_path)
        
        # Store results
        result = {
            'model_id': i,
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': compression_ratio,
            'final_train_loss': train_losses[-1]['total_loss'],
            'final_recon_loss': train_losses[-1]['recon_loss'],
            'final_kl_loss': train_losses[-1]['kl_loss'],
            'test_loss': test_metrics['total_loss'],
            'test_recon_loss': test_metrics['recon_loss'],
            'test_kl_loss': test_metrics['kl_loss'],
            'model_path': model_path
        }
        results.append(result)
        
        print(f"Compression ratio: {compression_ratio:.2f}x")
        print(f"Test reconstruction loss: {test_metrics['recon_loss']:.4f}")
    
    # Save results summary
    results_path = os.path.join(save_dir, 'training_results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nTraining complete! Results saved to {results_path}")
    return results

def main():
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    print("Loading MNIST data...")
    train_loader, test_loader = load_mnist_data(batch_size=128)
    
    # Generate model configurations
    print("Generating model configurations...")
    configurations = generate_model_configurations(num_models=50)
    print(f"Generated {len(configurations)} configurations")
    
    # Train models
    print("Starting training...")
    results = train_multiple_models(configurations, train_loader, test_loader, device=device)
    
    # Print summary
    print("\nTraining Summary:")
    print(f"Trained {len(results)} models")
    compression_ratios = [r['compression_ratio'] for r in results]
    recon_losses = [r['test_recon_loss'] for r in results]
    print(f"Compression ratios: {min(compression_ratios):.2f}x to {max(compression_ratios):.2f}x")
    print(f"Reconstruction losses: {min(recon_losses):.4f} to {max(recon_losses):.4f}")

if __name__ == "__main__":
    main()
