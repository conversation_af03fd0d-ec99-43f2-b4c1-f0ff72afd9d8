#!/usr/bin/env python3

import torch
import numpy as np
import matplotlib.pyplot as plt
from mnist_loader import get_sample_images
from enhanced_vae import EnhancedBetaVAE

def test_reconstruction_fidelity():
    """Test that VAE reconstructions preserve digit identity."""
    
    print("Testing VAE Reconstruction Fidelity")
    print("=" * 40)
    
    # Get sample images
    sample_images, sample_labels = get_sample_images(num_samples=10)
    
    # Test different compression levels
    test_configs = [
        {'latent_dim': 32, 'beta': 0.5},   # Low compression, high quality
        {'latent_dim': 16, 'beta': 1.0},   # Medium compression
        {'latent_dim': 8, 'beta': 2.0},    # Higher compression
        {'latent_dim': 4, 'beta': 4.0},    # High compression
        {'latent_dim': 2, 'beta': 8.0},    # Very high compression
    ]
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    for i, config in enumerate(test_configs):
        print(f"\nTest {i+1}: latent_dim={config['latent_dim']}, beta={config['beta']}")
        
        # Create and train a small model
        model = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        model.to(device)
        
        # Quick training on a few samples
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        images_tensor = torch.FloatTensor(sample_images).to(device)
        
        model.train()
        for epoch in range(50):  # Quick training
            optimizer.zero_grad()
            recon, mu, logvar, _ = model(images_tensor)
            loss, recon_loss, kl_loss = model.loss_function(recon, images_tensor, mu, logvar)
            loss.backward()
            optimizer.step()
            
            if epoch % 10 == 0:
                print(f"  Epoch {epoch}: Loss={loss.item():.2f}, Recon={recon_loss.item():.2f}")
        
        # Test reconstruction
        model.eval()
        with torch.no_grad():
            recon, _, _, _ = model(images_tensor)
            recon_np = recon.cpu().numpy()
        
        # Calculate metrics
        mse = np.mean((sample_images - recon_np) ** 2)
        compression_ratio = model.get_compression_ratio()
        
        print(f"  Compression ratio: {compression_ratio:.1f}x")
        print(f"  MSE: {mse:.6f}")
        
        # Visual check for first few images
        print("  Digit preservation check:")
        for j in range(min(5, len(sample_images))):
            original = sample_images[j]
            reconstructed = recon_np[j]
            label = sample_labels[j]
            
            # Simple digit preservation check - compare center region
            center_orig = original[10:18, 10:18].mean()
            center_recon = reconstructed[10:18, 10:18].mean()
            
            # Check if reconstruction maintains basic structure
            correlation = np.corrcoef(original.flatten(), reconstructed.flatten())[0, 1]
            
            status = "✓" if correlation > 0.5 else "✗"
            print(f"    Digit {label}: correlation={correlation:.3f} {status}")
        
        # Save a visualization
        if i == 0:  # Save visualization for first test
            save_reconstruction_comparison(sample_images[:5], recon_np[:5], sample_labels[:5], 
                                         f"test_reconstruction_{config['latent_dim']}d.png")
    
    print("\n" + "=" * 40)
    print("Reconstruction fidelity test complete!")

def save_reconstruction_comparison(originals, reconstructions, labels, filename):
    """Save a comparison of original vs reconstructed images."""
    
    fig, axes = plt.subplots(2, len(originals), figsize=(len(originals)*2, 4))
    
    for i in range(len(originals)):
        # Original
        axes[0, i].imshow(originals[i], cmap='gray')
        axes[0, i].set_title(f'Original\nDigit {labels[i]}')
        axes[0, i].axis('off')
        
        # Reconstructed
        axes[1, i].imshow(reconstructions[i], cmap='gray')
        axes[1, i].set_title(f'Reconstructed')
        axes[1, i].axis('off')
    
    plt.tight_layout()
    plt.savefig(filename, dpi=150, bbox_inches='tight')
    plt.close()
    print(f"  Saved comparison to {filename}")

def test_beta_effect():
    """Test how beta parameter affects reconstruction quality."""
    
    print("\nTesting Beta Parameter Effect")
    print("=" * 30)
    
    # Get a single test image
    sample_images, sample_labels = get_sample_images(num_samples=5)
    test_image = sample_images[0]
    test_label = sample_labels[0]
    
    print(f"Test image: Digit {test_label}")
    
    # Test different beta values
    beta_values = [0.1, 0.5, 1.0, 2.0, 4.0, 8.0, 16.0]
    latent_dim = 8  # Fixed latent dimension
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    results = []
    
    for beta in beta_values:
        print(f"\nTesting beta = {beta}")
        
        model = EnhancedBetaVAE(latent_dim=latent_dim, beta=beta)
        model.to(device)
        
        # Quick training
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        image_tensor = torch.FloatTensor(test_image).unsqueeze(0).to(device)
        
        model.train()
        for epoch in range(30):
            optimizer.zero_grad()
            recon, mu, logvar, _ = model(image_tensor)
            loss, recon_loss, kl_loss = model.loss_function(recon, image_tensor, mu, logvar)
            loss.backward()
            optimizer.step()
        
        # Evaluate
        model.eval()
        with torch.no_grad():
            recon, mu, logvar, _ = model(image_tensor)
            recon_np = recon.cpu().numpy()[0]
        
        mse = np.mean((test_image - recon_np) ** 2)
        correlation = np.corrcoef(test_image.flatten(), recon_np.flatten())[0, 1]
        
        results.append({
            'beta': beta,
            'mse': mse,
            'correlation': correlation,
            'reconstruction': recon_np
        })
        
        print(f"  MSE: {mse:.6f}, Correlation: {correlation:.3f}")
    
    # Find optimal beta
    best_result = max(results, key=lambda x: x['correlation'])
    print(f"\nBest beta: {best_result['beta']} (correlation: {best_result['correlation']:.3f})")
    
    return results

def main():
    """Main test function."""
    
    # Test reconstruction fidelity
    test_reconstruction_fidelity()
    
    # Test beta parameter effect
    beta_results = test_beta_effect()
    
    print("\n" + "=" * 50)
    print("All tests completed!")
    print("\nKey findings:")
    print("- Lower beta values preserve digit identity better")
    print("- Higher latent dimensions allow better reconstruction")
    print("- There's a trade-off between compression and fidelity")
    print("\nRecommendations:")
    print("- Use beta < 4.0 for recognizable reconstructions")
    print("- Use latent_dim >= 8 for reasonable quality")
    print("- Monitor correlation > 0.5 for digit preservation")

if __name__ == "__main__":
    main()
