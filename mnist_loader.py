import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import struct
import os

class MNISTDataset(Dataset):
    """Custom MNIST dataset loader for the raw IDX files."""
    
    def __init__(self, images_path, labels_path, transform=None):
        self.images = self._load_images(images_path)
        self.labels = self._load_labels(labels_path)
        self.transform = transform
        
    def _load_images(self, path):
        """Load images from IDX3 format."""
        with open(path, 'rb') as f:
            # Read header
            magic, num_images, rows, cols = struct.unpack('>IIII', f.read(16))
            if magic != 2051:
                raise ValueError(f'Invalid magic number {magic} in {path}')
            
            # Read image data
            images = np.frombuffer(f.read(), dtype=np.uint8)
            images = images.reshape(num_images, rows, cols)
            
        return images.astype(np.float32) / 255.0  # Normalize to [0, 1]
    
    def _load_labels(self, path):
        """Load labels from IDX1 format."""
        with open(path, 'rb') as f:
            # Read header
            magic, num_labels = struct.unpack('>II', f.read(8))
            if magic != 2049:
                raise ValueError(f'Invalid magic number {magic} in {path}')
            
            # Read label data
            labels = np.frombuffer(f.read(), dtype=np.uint8)
            
        return labels
    
    def __len__(self):
        return len(self.images)
    
    def __getitem__(self, idx):
        image = self.images[idx]
        label = self.labels[idx]
        
        if self.transform:
            image = self.transform(image)
        
        return torch.FloatTensor(image), torch.LongTensor([label])

def load_mnist_data(data_dir='MNIST', batch_size=128, shuffle=True):
    """Load MNIST training and test datasets."""

    # File paths - use the .idx3-ubyte and .idx1-ubyte files
    train_images_path = os.path.join(data_dir, 'train-images.idx3-ubyte')
    train_labels_path = os.path.join(data_dir, 'train-labels.idx1-ubyte')
    test_images_path = os.path.join(data_dir, 't10k-images.idx3-ubyte')
    test_labels_path = os.path.join(data_dir, 't10k-labels.idx1-ubyte')
    
    # Create datasets
    train_dataset = MNISTDataset(train_images_path, train_labels_path)
    test_dataset = MNISTDataset(test_images_path, test_labels_path)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=shuffle)
    test_loader = DataLoader(test_dataset, batch_size=batch_size, shuffle=False)
    
    return train_loader, test_loader

def get_sample_images(data_dir='MNIST', num_samples=100):
    """Get a sample of images for visualization and analysis."""
    test_images_path = os.path.join(data_dir, 't10k-images.idx3-ubyte')
    test_labels_path = os.path.join(data_dir, 't10k-labels.idx1-ubyte')
    
    dataset = MNISTDataset(test_images_path, test_labels_path)
    
    # Get diverse samples (10 from each digit class)
    samples_per_class = num_samples // 10
    sample_images = []
    sample_labels = []
    
    class_counts = {i: 0 for i in range(10)}
    
    for i in range(len(dataset)):
        image, label = dataset[i]
        label_val = label.item()
        
        if class_counts[label_val] < samples_per_class:
            sample_images.append(image.numpy())
            sample_labels.append(label_val)
            class_counts[label_val] += 1
            
        if len(sample_images) >= num_samples:
            break
    
    return np.array(sample_images), np.array(sample_labels)

if __name__ == "__main__":
    # Test the data loader
    print("Testing MNIST data loader...")
    train_loader, test_loader = load_mnist_data()
    
    print(f"Training batches: {len(train_loader)}")
    print(f"Test batches: {len(test_loader)}")
    
    # Test a batch
    for images, labels in train_loader:
        print(f"Batch shape: {images.shape}")
        print(f"Labels shape: {labels.shape}")
        print(f"Image range: [{images.min():.3f}, {images.max():.3f}]")
        break
    
    # Test sample images
    sample_images, sample_labels = get_sample_images(num_samples=20)
    print(f"Sample images shape: {sample_images.shape}")
    print(f"Sample labels: {sample_labels}")
