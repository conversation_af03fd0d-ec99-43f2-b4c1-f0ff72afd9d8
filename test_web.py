#!/usr/bin/env python3

import requests
import json
import sys

def test_web_application():
    """Test the web application endpoints."""
    
    base_url = "http://localhost:8000"
    
    print("Testing MNIST Compression Analysis Web Application")
    print("=" * 50)
    
    # Test main page
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✓ Main page loads successfully")
            if "MNIST Compression Analysis" in response.text:
                print("✓ Page contains expected title")
            else:
                print("✗ Page title not found")
        else:
            print(f"✗ Main page failed: {response.status_code}")
    except Exception as e:
        print(f"✗ Error loading main page: {e}")
        return False
    
    # Test JSON data files
    json_files = [
        "distortion_curves.json",
        "reconstructions.json", 
        "sample_images.json",
        "metadata.json"
    ]
    
    for json_file in json_files:
        try:
            response = requests.get(f"{base_url}/{json_file}")
            if response.status_code == 200:
                data = response.json()
                print(f"✓ {json_file} loads successfully")
                
                # Basic validation
                if json_file == "distortion_curves.json":
                    print(f"  - Contains {len(data)} curve data points")
                    if len(data) > 0:
                        required_fields = ['compression_ratio', 'mse', 'ssim', 'psnr']
                        if all(field in data[0] for field in required_fields):
                            print("  - Contains required fields")
                        else:
                            print("  - Missing required fields")
                
                elif json_file == "reconstructions.json":
                    print(f"  - Contains {len(data)} reconstruction sets")
                    if len(data) > 0:
                        required_fields = ['original_images', 'reconstructed_images', 'compression_ratio']
                        if all(field in data[0] for field in required_fields):
                            print("  - Contains required fields")
                        else:
                            print("  - Missing required fields")
                
                elif json_file == "sample_images.json":
                    print(f"  - Contains {len(data['images'])} sample images")
                    print(f"  - Image shape: {data['image_shape']}")
                
                elif json_file == "metadata.json":
                    print(f"  - Dataset: {data['dataset']}")
                    print(f"  - Number of models: {data['num_models']}")
                    
            else:
                print(f"✗ {json_file} failed: {response.status_code}")
        except Exception as e:
            print(f"✗ Error loading {json_file}: {e}")
    
    # Test CSS and JS files
    static_files = ["styles.css", "script.js"]
    
    for static_file in static_files:
        try:
            response = requests.get(f"{base_url}/{static_file}")
            if response.status_code == 200:
                print(f"✓ {static_file} loads successfully")
                print(f"  - Size: {len(response.text)} characters")
            else:
                print(f"✗ {static_file} failed: {response.status_code}")
        except Exception as e:
            print(f"✗ Error loading {static_file}: {e}")
    
    print("\n" + "=" * 50)
    print("Web application test complete!")
    print("\nThe application should now be accessible at:")
    print(f"🌐 {base_url}")
    print("\nFeatures to test in the browser:")
    print("- Interactive distortion-complexity curve")
    print("- Image reconstruction comparison")
    print("- Compression ratio controls")
    print("- Quality metric selection")
    print("- Hover effects and tooltips")
    
    return True

if __name__ == "__main__":
    test_web_application()
