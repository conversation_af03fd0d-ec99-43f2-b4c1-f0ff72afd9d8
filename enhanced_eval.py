import torch
import numpy as np
from scipy import interpolate
from scipy.optimize import curve_fit
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import json
import os
from tqdm import tqdm

from mnist_loader import get_sample_images
from enhanced_vae import EnhancedBetaVAE

def calculate_mse(original, reconstructed):
    """Calculate Mean Squared Error."""
    return np.mean((original - reconstructed) ** 2)

def calculate_ssim_batch(original, reconstructed):
    """Calculate SSIM for batch of images."""
    if len(original.shape) == 3:
        ssim_values = []
        for i in range(original.shape[0]):
            ssim_val = ssim(original[i], reconstructed[i], data_range=1.0)
            ssim_values.append(ssim_val)
        return np.mean(ssim_values)
    else:
        return ssim(original, reconstructed, data_range=1.0)

def calculate_psnr_batch(original, reconstructed):
    """Calculate PSNR for batch of images."""
    if len(original.shape) == 3:
        psnr_values = []
        for i in range(original.shape[0]):
            psnr_val = psnr(original[i], reconstructed[i], data_range=1.0)
            psnr_values.append(psnr_val)
        return np.mean(psnr_values)
    else:
        return psnr(original, reconstructed, data_range=1.0)

def estimate_enhanced_compression_bits(model, images, device='cpu'):
    """Estimate compression bits for enhanced models."""
    model.eval()
    model.to(device)
    
    with torch.no_grad():
        if isinstance(images, np.ndarray):
            images = torch.FloatTensor(images).to(device)
        
        # Encode to latent space
        mu, logvar = model.encoder(images)
        
        # For compression estimation, quantize the latent representation
        # Use 8-bit quantization for the latent space
        bits_per_dimension = 8
        total_bits = model.latent_dim * bits_per_dimension
        
        # Add overhead for variance (simplified)
        overhead_bits = model.latent_dim * 1  # 1 bit per dimension for variance
        
        total_compressed_bits = total_bits + overhead_bits
        original_bits = 28 * 28 * 8  # 8 bits per pixel
        
        compression_ratio = original_bits / total_compressed_bits
        
        return {
            'compressed_bits': total_compressed_bits,
            'original_bits': original_bits,
            'compression_ratio': compression_ratio,
            'bits_per_pixel': total_compressed_bits / (28 * 28)
        }

def evaluate_enhanced_model_quality(model, images, device='cpu'):
    """Evaluate reconstruction quality of enhanced model."""
    model.eval()
    model.to(device)
    
    with torch.no_grad():
        if isinstance(images, np.ndarray):
            images_tensor = torch.FloatTensor(images).to(device)
        else:
            images_tensor = images.to(device)
        
        # Get reconstructions
        reconstructed, _, _, _ = model(images_tensor)
        
        # Convert back to numpy for evaluation
        if isinstance(images, np.ndarray):
            original_np = images
        else:
            original_np = images.cpu().numpy()
        
        reconstructed_np = reconstructed.cpu().numpy()
        
        # Calculate metrics
        mse = calculate_mse(original_np, reconstructed_np)
        ssim_score = calculate_ssim_batch(original_np, reconstructed_np)
        psnr_score = calculate_psnr_batch(original_np, reconstructed_np)
        
        return {
            'mse': float(mse),
            'ssim': float(ssim_score),
            'psnr': float(psnr_score),
            'reconstructions': reconstructed_np
        }

def fit_curve_to_data(x_data, y_data, curve_type='spline'):
    """Fit a smooth curve to the distortion-complexity data."""
    
    # Sort data by x values
    sorted_indices = np.argsort(x_data)
    x_sorted = np.array(x_data)[sorted_indices]
    y_sorted = np.array(y_data)[sorted_indices]
    
    # Remove duplicates and NaN values
    mask = ~(np.isnan(x_sorted) | np.isnan(y_sorted))
    x_clean = x_sorted[mask]
    y_clean = y_sorted[mask]
    
    if len(x_clean) < 4:
        return x_clean, y_clean, x_clean, y_clean
    
    if curve_type == 'spline':
        # Use cubic spline interpolation
        try:
            # Create more points for smooth curve
            x_smooth = np.linspace(x_clean.min(), x_clean.max(), 200)
            
            # Use univariate spline
            spline = interpolate.UnivariateSpline(x_clean, y_clean, s=len(x_clean)*0.1)
            y_smooth = spline(x_smooth)
            
            return x_clean, y_clean, x_smooth, y_smooth
        except:
            # Fallback to linear interpolation
            x_smooth = np.linspace(x_clean.min(), x_clean.max(), 200)
            y_smooth = np.interp(x_smooth, x_clean, y_clean)
            return x_clean, y_clean, x_smooth, y_smooth
    
    elif curve_type == 'exponential':
        # Fit exponential decay curve: y = a * exp(-b * x) + c
        def exp_func(x, a, b, c):
            return a * np.exp(-b * x) + c
        
        try:
            # Initial guess
            p0 = [y_clean.max() - y_clean.min(), 0.01, y_clean.min()]
            popt, _ = curve_fit(exp_func, x_clean, y_clean, p0=p0, maxfev=1000)
            
            x_smooth = np.linspace(x_clean.min(), x_clean.max(), 200)
            y_smooth = exp_func(x_smooth, *popt)
            
            return x_clean, y_clean, x_smooth, y_smooth
        except:
            # Fallback to spline
            return fit_curve_to_data(x_data, y_data, 'spline')
    
    else:
        # Default: return original data
        return x_clean, y_clean, x_clean, y_clean

def create_enhanced_distortion_curves(model_results_path, sample_images, device='cpu'):
    """Create enhanced distortion curve data with 500 points."""
    
    # Load training results
    with open(model_results_path, 'r') as f:
        results = json.load(f)
    
    curve_data = []
    
    print(f"Evaluating {len(results)} enhanced models for distortion curves...")
    
    for result in tqdm(results):
        model_path = result['model_path']
        
        # Load model
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint['config']
        
        model = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Evaluate compression
        compression_info = estimate_enhanced_compression_bits(model, sample_images, device)
        
        # Evaluate quality
        quality_info = evaluate_enhanced_model_quality(model, sample_images, device)
        
        # Combine data
        curve_point = {
            'model_id': result['model_id'],
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': compression_info['compression_ratio'],
            'bits_per_pixel': compression_info['bits_per_pixel'],
            'compressed_bits': compression_info['compressed_bits'],
            'mse': quality_info['mse'],
            'ssim': quality_info['ssim'],
            'psnr': quality_info['psnr'],
            'train_recon_loss': result['final_recon_loss'],
            'test_recon_loss': result['test_recon_loss']
        }
        
        curve_data.append(curve_point)
    
    # Fit curves to the data
    print("Fitting smooth curves to distortion data...")
    
    # Extract data for curve fitting
    compression_ratios = [d['compression_ratio'] for d in curve_data]
    mse_values = [d['mse'] for d in curve_data]
    ssim_values = [d['ssim'] for d in curve_data]
    psnr_values = [d['psnr'] for d in curve_data]
    
    # Fit curves for each metric
    curves = {}
    
    # MSE curve (should increase with compression)
    x_orig, y_orig, x_smooth, y_smooth = fit_curve_to_data(compression_ratios, mse_values, 'spline')
    curves['mse'] = {
        'original_x': x_orig.tolist(),
        'original_y': y_orig.tolist(),
        'smooth_x': x_smooth.tolist(),
        'smooth_y': y_smooth.tolist()
    }
    
    # SSIM curve (should decrease with compression)
    x_orig, y_orig, x_smooth, y_smooth = fit_curve_to_data(compression_ratios, ssim_values, 'spline')
    curves['ssim'] = {
        'original_x': x_orig.tolist(),
        'original_y': y_orig.tolist(),
        'smooth_x': x_smooth.tolist(),
        'smooth_y': y_smooth.tolist()
    }
    
    # PSNR curve (should decrease with compression)
    x_orig, y_orig, x_smooth, y_smooth = fit_curve_to_data(compression_ratios, psnr_values, 'spline')
    curves['psnr'] = {
        'original_x': x_orig.tolist(),
        'original_y': y_orig.tolist(),
        'smooth_x': x_smooth.tolist(),
        'smooth_y': y_smooth.tolist()
    }
    
    return curve_data, curves

def generate_enhanced_reconstructions(model_results_path, sample_images, sample_labels, 
                                    num_examples=20, device='cpu'):
    """Generate reconstructions for enhanced models."""
    
    # Load training results
    with open(model_results_path, 'r') as f:
        results = json.load(f)
    
    # Select representative models across compression range
    results_sorted = sorted(results, key=lambda x: x['compression_ratio'])
    selected_indices = np.linspace(0, len(results_sorted)-1, 15).astype(int)
    selected_results = [results_sorted[i] for i in selected_indices]
    
    reconstructions_data = []
    
    print("Generating enhanced sample reconstructions...")
    
    for result in tqdm(selected_results):
        model_path = result['model_path']
        
        # Load model
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint['config']
        
        model = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Get reconstructions for sample images
        quality_info = evaluate_enhanced_model_quality(model, sample_images[:num_examples], device)
        
        reconstruction_data = {
            'model_id': result['model_id'],
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': result['compression_ratio'],
            'original_images': sample_images[:num_examples].tolist(),
            'reconstructed_images': quality_info['reconstructions'].tolist(),
            'labels': sample_labels[:num_examples].tolist(),
            'mse': quality_info['mse'],
            'ssim': quality_info['ssim'],
            'psnr': quality_info['psnr']
        }
        
        reconstructions_data.append(reconstruction_data)
    
    return reconstructions_data

def main():
    """Main evaluation function."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Get sample images for evaluation
    print("Loading sample images...")
    sample_images, sample_labels = get_sample_images(num_samples=100)
    
    # Check for enhanced model results
    model_results_path = 'enhanced_models/enhanced_training_results.json'
    if os.path.exists(model_results_path):
        print("Creating enhanced distortion curve data...")
        curve_data, curves = create_enhanced_distortion_curves(model_results_path, sample_images, device)
        
        # Save curve data
        with open('enhanced_models/enhanced_distortion_curves.json', 'w') as f:
            json.dump(curve_data, f, indent=2)
        
        # Save fitted curves
        with open('enhanced_models/fitted_curves.json', 'w') as f:
            json.dump(curves, f, indent=2)
        
        print("Generating enhanced sample reconstructions...")
        reconstructions_data = generate_enhanced_reconstructions(
            model_results_path, sample_images, sample_labels, device=device
        )
        
        # Save reconstructions data
        with open('enhanced_models/enhanced_reconstructions.json', 'w') as f:
            json.dump(reconstructions_data, f, indent=2)
        
        print("Enhanced evaluation complete!")
        print(f"Curve data saved to enhanced_models/enhanced_distortion_curves.json")
        print(f"Fitted curves saved to enhanced_models/fitted_curves.json")
        print(f"Reconstructions saved to enhanced_models/enhanced_reconstructions.json")
        
        # Print summary
        compression_ratios = [d['compression_ratio'] for d in curve_data]
        mse_values = [d['mse'] for d in curve_data]
        ssim_values = [d['ssim'] for d in curve_data]
        
        print(f"\nSummary:")
        print(f"Number of models evaluated: {len(curve_data)}")
        print(f"Compression ratios: {min(compression_ratios):.1f}x to {max(compression_ratios):.1f}x")
        print(f"MSE range: {min(mse_values):.6f} to {max(mse_values):.6f}")
        print(f"SSIM range: {min(ssim_values):.4f} to {max(ssim_values):.4f}")
        
    else:
        print(f"Enhanced training results not found at {model_results_path}")
        print("Please run enhanced_train.py first to train the enhanced models.")

if __name__ == "__main__":
    main()
