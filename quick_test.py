#!/usr/bin/env python3

import torch
import numpy as np
from train_vae import generate_model_configurations, train_multiple_models
from mnist_loader import load_mnist_data
from compression_eval import create_distortion_curve_data, generate_sample_reconstructions, get_sample_images

def quick_test():
    """Run a quick test with fewer models to verify everything works."""
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    print("Loading MNIST data...")
    train_loader, test_loader = load_mnist_data(batch_size=64)  # Smaller batch size for testing
    
    # Generate fewer model configurations for testing
    print("Generating test configurations...")
    configurations = generate_model_configurations(num_models=6)  # Just 6 models for testing
    print(f"Generated {len(configurations)} test configurations")
    
    # Train models
    print("Starting training...")
    results = train_multiple_models(configurations, train_loader, test_loader, device=device, save_dir='test_models')
    
    print(f"\nTrained {len(results)} models successfully!")
    
    # Test evaluation
    print("Testing evaluation...")
    sample_images, sample_labels = get_sample_images(num_samples=20)
    
    curve_data = create_distortion_curve_data('test_models/training_results.json', sample_images, device)
    print(f"Generated curve data for {len(curve_data)} models")
    
    reconstructions_data = generate_sample_reconstructions(
        'test_models/training_results.json', sample_images, sample_labels, 
        num_examples=5, device=device
    )
    print(f"Generated reconstructions for {len(reconstructions_data)} models")
    
    print("\nQuick test completed successfully!")
    return True

if __name__ == "__main__":
    quick_test()
