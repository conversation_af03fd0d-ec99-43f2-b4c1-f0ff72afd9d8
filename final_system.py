#!/usr/bin/env python3

import torch
import numpy as np
import json
import os
from tqdm import tqdm
from scipy import interpolate

from mnist_loader import load_mnist_data, get_sample_images
from enhanced_vae import EnhancedBetaVAE

def create_optimized_configurations(num_models=500):
    """Create optimized configurations that ensure good reconstruction fidelity."""
    
    configurations = []
    
    # Strategy: Use lower beta values to ensure digit preservation
    # and wider range of latent dimensions for compression variety
    
    # Latent dimensions from 1 to 64
    latent_dims = np.unique(np.logspace(np.log10(1), np.log10(64), 25).astype(int))
    
    # Beta values from 0.01 to 10 (much lower max to preserve fidelity)
    betas = np.logspace(np.log10(0.01), np.log10(10), 20)
    
    # Generate all combinations
    for latent_dim in latent_dims:
        for beta in betas:
            configurations.append({
                'latent_dim': max(1, int(latent_dim)),
                'beta': float(beta)
            })
    
    # Sample evenly if we have too many
    if len(configurations) > num_models:
        indices = np.linspace(0, len(configurations)-1, num_models).astype(int)
        configurations = [configurations[i] for i in indices]
    
    # Sort by compression ratio for better training progression
    configurations.sort(key=lambda x: EnhancedBetaVAE(latent_dim=x['latent_dim']).get_compression_ratio())
    
    return configurations

def train_optimized_model(model, train_loader, num_epochs=20, device='cpu'):
    """Train model with optimized settings for reconstruction fidelity."""
    
    model.to(device)
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3, weight_decay=1e-5)
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=3, factor=0.7)
    
    model.train()
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        epoch_recon_loss = 0
        
        for batch_idx, (data, _) in enumerate(train_loader):
            data = data.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            recon_batch, mu, logvar, _ = model(data)
            
            # Compute loss with step information
            step = epoch * len(train_loader) + batch_idx
            loss, recon_loss, kl_loss = model.loss_function(recon_batch, data, mu, logvar, step)
            
            # Backward pass
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            epoch_loss += loss.item()
            epoch_recon_loss += recon_loss.item()
        
        avg_loss = epoch_loss / len(train_loader.dataset)
        avg_recon_loss = epoch_recon_loss / len(train_loader.dataset)
        
        scheduler.step(avg_loss)
        
        if epoch % 5 == 0:
            print(f'Epoch {epoch:2d}: Loss={avg_loss:.4f}, Recon={avg_recon_loss:.4f}')
    
    return avg_loss, avg_recon_loss

def evaluate_reconstruction_quality(model, test_images, device='cpu'):
    """Evaluate reconstruction quality with fidelity metrics."""
    
    model.eval()
    model.to(device)
    
    with torch.no_grad():
        if isinstance(test_images, np.ndarray):
            images_tensor = torch.FloatTensor(test_images).to(device)
        else:
            images_tensor = test_images.to(device)
        
        # Get reconstructions
        reconstructed, mu, logvar, _ = model(images_tensor)
        
        # Convert to numpy
        if isinstance(test_images, np.ndarray):
            original_np = test_images
        else:
            original_np = test_images.cpu().numpy()
        
        reconstructed_np = reconstructed.cpu().numpy()
        
        # Calculate metrics
        mse = np.mean((original_np - reconstructed_np) ** 2)
        
        # Calculate correlation (digit preservation metric)
        correlations = []
        for i in range(len(original_np)):
            corr = np.corrcoef(original_np[i].flatten(), reconstructed_np[i].flatten())[0, 1]
            if not np.isnan(corr):
                correlations.append(corr)
        
        avg_correlation = np.mean(correlations) if correlations else 0.0
        
        # SSIM calculation (simplified)
        from skimage.metrics import structural_similarity as ssim
        ssim_values = []
        for i in range(len(original_np)):
            ssim_val = ssim(original_np[i], reconstructed_np[i], data_range=1.0)
            ssim_values.append(ssim_val)
        avg_ssim = np.mean(ssim_values)
        
        return {
            'mse': float(mse),
            'ssim': float(avg_ssim),
            'correlation': float(avg_correlation),
            'reconstructions': reconstructed_np,
            'fidelity_score': float(avg_correlation * avg_ssim)  # Combined fidelity metric
        }

def run_comprehensive_analysis(num_models=100):
    """Run comprehensive analysis with optimized settings."""
    
    print(f"Running Comprehensive MNIST Compression Analysis")
    print(f"Training {num_models} optimized models...")
    print("=" * 60)
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    train_loader, test_loader = load_mnist_data(batch_size=64)
    sample_images, sample_labels = get_sample_images(num_samples=50)
    
    # Generate optimized configurations
    configurations = create_optimized_configurations(num_models=num_models)
    print(f"Generated {len(configurations)} optimized configurations")
    
    # Create results directory
    os.makedirs('final_results', exist_ok=True)
    
    results = []
    
    for i, config in enumerate(tqdm(configurations, desc="Training models")):
        print(f"\nModel {i+1}/{len(configurations)}: latent_dim={config['latent_dim']}, beta={config['beta']:.3f}")
        
        # Create model
        model = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        compression_ratio = model.get_compression_ratio()
        
        print(f"Target compression: {compression_ratio:.1f}x")
        
        # Train model
        train_loss, train_recon_loss = train_optimized_model(model, train_loader, num_epochs=15, device=device)
        
        # Evaluate on sample images
        quality_metrics = evaluate_reconstruction_quality(model, sample_images, device=device)
        
        # Store results
        result = {
            'model_id': i,
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': compression_ratio,
            'train_loss': train_loss,
            'train_recon_loss': train_recon_loss,
            'mse': quality_metrics['mse'],
            'ssim': quality_metrics['ssim'],
            'correlation': quality_metrics['correlation'],
            'fidelity_score': quality_metrics['fidelity_score'],
            'bits_per_pixel': (28 * 28 * 8) / compression_ratio / (28 * 28)
        }
        
        results.append(result)
        
        print(f"Compression: {compression_ratio:.1f}x, Fidelity: {quality_metrics['fidelity_score']:.3f}")
        
        # Save checkpoint every 25 models
        if (i + 1) % 25 == 0:
            checkpoint_path = f'final_results/checkpoint_{i+1}.json'
            with open(checkpoint_path, 'w') as f:
                json.dump(results, f, indent=2)
    
    # Save final results
    results_path = 'final_results/comprehensive_results.json'
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    # Create fitted curves
    print("\nFitting smooth curves to data...")
    curves = create_fitted_curves(results)
    
    with open('final_results/fitted_curves.json', 'w') as f:
        json.dump(curves, f, indent=2)
    
    # Generate sample reconstructions
    print("Generating sample reconstructions...")
    reconstructions = generate_final_reconstructions(results, sample_images, sample_labels, device)
    
    with open('final_results/sample_reconstructions.json', 'w') as f:
        json.dump(reconstructions, f, indent=2)
    
    print(f"\nAnalysis complete! Results saved to final_results/")
    
    # Print summary
    compression_ratios = [r['compression_ratio'] for r in results]
    fidelity_scores = [r['fidelity_score'] for r in results]
    correlations = [r['correlation'] for r in results]
    
    print(f"\nSummary:")
    print(f"Models trained: {len(results)}")
    print(f"Compression range: {min(compression_ratios):.1f}x to {max(compression_ratios):.1f}x")
    print(f"Fidelity scores: {min(fidelity_scores):.3f} to {max(fidelity_scores):.3f}")
    print(f"Correlations: {min(correlations):.3f} to {max(correlations):.3f}")
    print(f"Models with good fidelity (>0.5): {sum(1 for f in fidelity_scores if f > 0.5)}")
    
    return results

def create_fitted_curves(results):
    """Create fitted curves from results."""
    
    compression_ratios = [r['compression_ratio'] for r in results]
    mse_values = [r['mse'] for r in results]
    ssim_values = [r['ssim'] for r in results]
    correlation_values = [r['correlation'] for r in results]
    
    # Sort by compression ratio
    sorted_indices = np.argsort(compression_ratios)
    x_sorted = np.array(compression_ratios)[sorted_indices]
    
    curves = {}
    
    for metric, values in [('mse', mse_values), ('ssim', ssim_values), ('correlation', correlation_values)]:
        y_sorted = np.array(values)[sorted_indices]
        
        # Create smooth curve
        x_smooth = np.linspace(x_sorted.min(), x_sorted.max(), 200)
        
        try:
            spline = interpolate.UnivariateSpline(x_sorted, y_sorted, s=len(x_sorted)*0.1)
            y_smooth = spline(x_smooth)
        except:
            y_smooth = np.interp(x_smooth, x_sorted, y_sorted)
        
        curves[metric] = {
            'original_x': x_sorted.tolist(),
            'original_y': y_sorted.tolist(),
            'smooth_x': x_smooth.tolist(),
            'smooth_y': y_smooth.tolist()
        }
    
    return curves

def generate_final_reconstructions(results, sample_images, sample_labels, device):
    """Generate final reconstructions for visualization."""
    
    # Select representative models
    results_sorted = sorted(results, key=lambda x: x['compression_ratio'])
    selected_indices = np.linspace(0, len(results_sorted)-1, 10).astype(int)
    selected_results = [results_sorted[i] for i in selected_indices]
    
    reconstructions = []
    
    for result in selected_results:
        # Create model with same configuration
        model = EnhancedBetaVAE(latent_dim=result['latent_dim'], beta=result['beta'])
        
        # Quick training on sample images for reconstruction
        model.to(device)
        optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
        images_tensor = torch.FloatTensor(sample_images[:10]).to(device)
        
        model.train()
        for epoch in range(20):
            optimizer.zero_grad()
            recon, mu, logvar, _ = model(images_tensor)
            loss, _, _ = model.loss_function(recon, images_tensor, mu, logvar)
            loss.backward()
            optimizer.step()
        
        # Get final reconstructions
        model.eval()
        with torch.no_grad():
            recon, _, _, _ = model(images_tensor)
            recon_np = recon.cpu().numpy()
        
        reconstructions.append({
            'model_id': result['model_id'],
            'latent_dim': result['latent_dim'],
            'beta': result['beta'],
            'compression_ratio': result['compression_ratio'],
            'fidelity_score': result['fidelity_score'],
            'original_images': sample_images[:10].tolist(),
            'reconstructed_images': recon_np.tolist(),
            'labels': sample_labels[:10].tolist()
        })
    
    return reconstructions

def main():
    """Main function."""
    
    print("Choose analysis mode:")
    print("1. Quick test (25 models)")
    print("2. Medium analysis (100 models)")
    print("3. Full analysis (500 models)")
    
    choice = input("Enter choice (1, 2, or 3): ").strip()
    
    if choice == "1":
        results = run_comprehensive_analysis(num_models=25)
    elif choice == "2":
        results = run_comprehensive_analysis(num_models=100)
    elif choice == "3":
        results = run_comprehensive_analysis(num_models=500)
    else:
        print("Invalid choice. Running quick test...")
        results = run_comprehensive_analysis(num_models=25)
    
    print("\nFinal system ready!")
    print("Results saved to final_results/ directory")

if __name__ == "__main__":
    main()
