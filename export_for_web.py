import json
import numpy as np
import os
from mnist_loader import get_sample_images
from compression_eval import create_distortion_curve_data, generate_sample_reconstructions
import torch

def create_mock_data():
    """Create mock data for web development while models are training."""
    
    # Generate mock distortion curve data
    num_points = 50
    
    # Create realistic-looking compression ratios and quality metrics
    compression_ratios = np.logspace(np.log10(2), np.log10(50), num_points)
    
    # MSE should generally increase with higher compression (lower quality)
    base_mse = 0.001 + 0.05 * (compression_ratios / max(compression_ratios)) ** 2
    mse_values = base_mse + np.random.normal(0, 0.005, num_points)
    mse_values = np.clip(mse_values, 0.001, 0.1)
    
    # SSIM should generally decrease with higher compression
    base_ssim = 0.95 - 0.3 * (compression_ratios / max(compression_ratios)) ** 0.5
    ssim_values = base_ssim + np.random.normal(0, 0.02, num_points)
    ssim_values = np.clip(ssim_values, 0.4, 0.98)
    
    # PSNR should generally decrease with higher compression
    base_psnr = 35 - 15 * (compression_ratios / max(compression_ratios)) ** 0.7
    psnr_values = base_psnr + np.random.normal(0, 1, num_points)
    psnr_values = np.clip(psnr_values, 15, 40)
    
    mock_curve_data = []
    for i in range(num_points):
        mock_curve_data.append({
            'model_id': i,
            'latent_dim': int(4 + (60 * i / num_points)),
            'beta': float(0.1 + (99.9 * i / num_points)),
            'compression_ratio': float(compression_ratios[i]),
            'bits_per_pixel': float(8 / compression_ratios[i]),
            'compressed_bits': int(6272 / compression_ratios[i]),
            'mse': float(mse_values[i]),
            'ssim': float(ssim_values[i]),
            'psnr': float(psnr_values[i]),
            'train_recon_loss': float(mse_values[i] * 1000),
            'test_recon_loss': float(mse_values[i] * 1100)
        })
    
    return mock_curve_data

def create_mock_reconstructions():
    """Create mock reconstruction data."""
    
    # Get some real sample images
    sample_images, sample_labels = get_sample_images(num_samples=20)
    
    # Create mock reconstructions for different compression levels
    mock_reconstructions = []
    
    compression_levels = [2, 5, 10, 20, 35, 50]
    
    for i, comp_ratio in enumerate(compression_levels):
        # Add noise to simulate compression artifacts
        noise_level = 0.01 + 0.05 * (comp_ratio / max(compression_levels))
        
        reconstructed_images = []
        for img in sample_images:
            # Add some noise and blur to simulate compression
            noisy_img = img + np.random.normal(0, noise_level, img.shape)
            noisy_img = np.clip(noisy_img, 0, 1)
            reconstructed_images.append(noisy_img)
        
        mock_reconstructions.append({
            'model_id': i,
            'latent_dim': int(4 + (60 * i / len(compression_levels))),
            'beta': float(0.1 + (99.9 * i / len(compression_levels))),
            'compression_ratio': float(comp_ratio),
            'original_images': sample_images.tolist(),
            'reconstructed_images': np.array(reconstructed_images).tolist(),
            'labels': sample_labels.tolist(),
            'mse': float(0.001 + 0.05 * (comp_ratio / max(compression_levels))),
            'ssim': float(0.95 - 0.3 * (comp_ratio / max(compression_levels))),
            'psnr': float(35 - 15 * (comp_ratio / max(compression_levels)))
        })
    
    return mock_reconstructions

def export_web_data(use_mock=True):
    """Export data for web visualization."""
    
    # Create web directory
    web_dir = 'web'
    os.makedirs(web_dir, exist_ok=True)
    
    if use_mock or not os.path.exists('models/training_results.json'):
        print("Using mock data for web development...")
        
        # Create mock data
        curve_data = create_mock_data()
        reconstructions_data = create_mock_reconstructions()
        
    else:
        print("Using real trained model data...")
        
        # Load real data
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        sample_images, sample_labels = get_sample_images(num_samples=100)
        
        curve_data = create_distortion_curve_data('models/training_results.json', sample_images, device)
        reconstructions_data = generate_sample_reconstructions(
            'models/training_results.json', sample_images, sample_labels, device=device
        )
    
    # Export curve data
    with open(os.path.join(web_dir, 'distortion_curves.json'), 'w') as f:
        json.dump(curve_data, f, indent=2)
    
    # Export reconstructions data
    with open(os.path.join(web_dir, 'reconstructions.json'), 'w') as f:
        json.dump(reconstructions_data, f, indent=2)
    
    # Create metadata
    metadata = {
        'dataset': 'MNIST',
        'num_models': len(curve_data),
        'num_reconstruction_examples': len(reconstructions_data),
        'compression_ratio_range': [
            min(d['compression_ratio'] for d in curve_data),
            max(d['compression_ratio'] for d in curve_data)
        ],
        'mse_range': [
            min(d['mse'] for d in curve_data),
            max(d['mse'] for d in curve_data)
        ],
        'ssim_range': [
            min(d['ssim'] for d in curve_data),
            max(d['ssim'] for d in curve_data)
        ],
        'psnr_range': [
            min(d['psnr'] for d in curve_data),
            max(d['psnr'] for d in curve_data)
        ],
        'image_dimensions': [28, 28],
        'original_bits_per_image': 6272,
        'description': 'MNIST digit compression using Beta-VAE with varying compression ratios'
    }
    
    with open(os.path.join(web_dir, 'metadata.json'), 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Web data exported to {web_dir}/")
    print(f"- distortion_curves.json: {len(curve_data)} data points")
    print(f"- reconstructions.json: {len(reconstructions_data)} reconstruction sets")
    print(f"- metadata.json: Dataset and range information")
    
    return curve_data, reconstructions_data, metadata

def create_sample_images_for_web():
    """Create a separate file with sample images for the web interface."""
    
    # Get diverse sample images
    sample_images, sample_labels = get_sample_images(num_samples=100)
    
    # Create data structure for web
    web_samples = {
        'images': sample_images.tolist(),
        'labels': sample_labels.tolist(),
        'image_shape': [28, 28],
        'num_samples': len(sample_images),
        'classes': list(range(10)),
        'class_names': [str(i) for i in range(10)]
    }
    
    # Save to web directory
    web_dir = 'web'
    os.makedirs(web_dir, exist_ok=True)
    
    with open(os.path.join(web_dir, 'sample_images.json'), 'w') as f:
        json.dump(web_samples, f, indent=2)
    
    print(f"Sample images exported to {web_dir}/sample_images.json")
    return web_samples

def main():
    """Main export function."""
    print("Exporting data for web visualization...")
    
    # Export main data
    curve_data, reconstructions_data, metadata = export_web_data(use_mock=True)
    
    # Export sample images
    sample_data = create_sample_images_for_web()
    
    print("\nExport complete!")
    print("Ready to create web visualization.")

if __name__ == "__main__":
    main()
