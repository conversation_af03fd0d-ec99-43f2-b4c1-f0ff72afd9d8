import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

class Encoder(nn.Module):
    """Encoder network for Beta-VAE."""
    
    def __init__(self, latent_dim=20):
        super(Encoder, self).__init__()
        self.latent_dim = latent_dim
        
        # Convolutional layers
        self.conv1 = nn.Conv2d(1, 32, kernel_size=4, stride=2, padding=1)  # 28x28 -> 14x14
        self.conv2 = nn.Conv2d(32, 64, kernel_size=4, stride=2, padding=1)  # 14x14 -> 7x7
        self.conv3 = nn.Conv2d(64, 128, kernel_size=4, stride=2, padding=1)  # 7x7 -> 4x4

        # Calculate flattened size: 128 * 3 * 3 = 1152 (actual size after conv operations)
        self.fc_input_size = 128 * 3 * 3
        
        # Fully connected layers for mean and log variance
        self.fc_mu = nn.Linear(self.fc_input_size, latent_dim)
        self.fc_logvar = nn.Linear(self.fc_input_size, latent_dim)
        
    def forward(self, x):
        # Add channel dimension if needed
        if len(x.shape) == 3:
            x = x.unsqueeze(1)
        
        # Convolutional layers with ReLU activation
        x = F.relu(self.conv1(x))
        x = F.relu(self.conv2(x))
        x = F.relu(self.conv3(x))
        
        # Flatten
        x = x.view(x.size(0), -1)
        
        # Get mean and log variance
        mu = self.fc_mu(x)
        logvar = self.fc_logvar(x)
        
        return mu, logvar

class Decoder(nn.Module):
    """Decoder network for Beta-VAE."""
    
    def __init__(self, latent_dim=20):
        super(Decoder, self).__init__()
        self.latent_dim = latent_dim
        
        # Fully connected layer to expand latent vector
        self.fc = nn.Linear(latent_dim, 128 * 3 * 3)
        
        # Transposed convolutional layers
        self.deconv1 = nn.ConvTranspose2d(128, 64, kernel_size=4, stride=2, padding=1)  # 3x3 -> 6x6
        self.deconv2 = nn.ConvTranspose2d(64, 32, kernel_size=4, stride=2, padding=1)   # 6x6 -> 12x12
        self.deconv3 = nn.ConvTranspose2d(32, 1, kernel_size=5, stride=2, padding=1)    # 12x12 -> 25x25
        self.deconv4 = nn.ConvTranspose2d(1, 1, kernel_size=4, stride=1, padding=0)     # 25x25 -> 28x28
        
    def forward(self, z):
        # Expand latent vector
        x = F.relu(self.fc(z))
        x = x.view(x.size(0), 128, 3, 3)
        
        # Transposed convolutional layers
        x = F.relu(self.deconv1(x))
        x = F.relu(self.deconv2(x))
        x = F.relu(self.deconv3(x))
        x = torch.sigmoid(self.deconv4(x))  # Output in [0, 1]
        
        return x.squeeze(1)  # Remove channel dimension

class BetaVAE(nn.Module):
    """Beta-VAE model for controllable compression."""

    def __init__(self, latent_dim=20, beta=1.0):
        super(BetaVAE, self).__init__()
        self.latent_dim = latent_dim
        self.beta = beta

        self.encoder = Encoder(latent_dim)
        self.decoder = Decoder(latent_dim)

        # Add capacity parameter for better control
        self.capacity = 0.0  # Will be increased during training
        
    def reparameterize(self, mu, logvar):
        """Reparameterization trick for sampling from latent distribution."""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def forward(self, x):
        # Encode
        mu, logvar = self.encoder(x)
        
        # Reparameterize
        z = self.reparameterize(mu, logvar)
        
        # Decode
        recon_x = self.decoder(z)
        
        return recon_x, mu, logvar, z
    
    def encode(self, x):
        """Encode input to latent space."""
        mu, logvar = self.encoder(x)
        return self.reparameterize(mu, logvar)
    
    def decode(self, z):
        """Decode latent vector to reconstruction."""
        return self.decoder(z)
    
    def loss_function(self, recon_x, x, mu, logvar):
        """Compute Beta-VAE loss with capacity control."""
        # Ensure inputs are in [0, 1] range
        x = torch.clamp(x, 0, 1)
        recon_x = torch.clamp(recon_x, 1e-8, 1 - 1e-8)  # Avoid log(0)

        # Reconstruction loss (Mean Squared Error for better gradient flow)
        recon_loss = F.mse_loss(recon_x, x, reduction='sum')

        # KL divergence loss with capacity constraint
        kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())

        # Apply capacity constraint to prevent posterior collapse
        kl_loss = torch.abs(kl_loss - self.capacity)

        # Total loss with beta weighting (use smaller beta for better reconstruction)
        effective_beta = min(self.beta, 4.0)  # Cap beta to prevent over-regularization
        total_loss = recon_loss + effective_beta * kl_loss

        return total_loss, recon_loss, kl_loss
    
    def get_compression_ratio(self):
        """Calculate theoretical compression ratio."""
        # Original image: 28 * 28 * 8 bits = 6272 bits
        # Compressed: latent_dim * 32 bits (assuming float32)
        original_bits = 28 * 28 * 8
        compressed_bits = self.latent_dim * 32
        return original_bits / compressed_bits
    
    def set_beta(self, beta):
        """Update beta parameter for different compression ratios."""
        self.beta = beta

def create_beta_vae_models(latent_dims, betas):
    """Create multiple Beta-VAE models with different configurations."""
    models = []
    
    for latent_dim in latent_dims:
        for beta in betas:
            model = BetaVAE(latent_dim=latent_dim, beta=beta)
            models.append({
                'model': model,
                'latent_dim': latent_dim,
                'beta': beta,
                'compression_ratio': model.get_compression_ratio()
            })
    
    return models

if __name__ == "__main__":
    # Test the Beta-VAE model
    print("Testing Beta-VAE model...")
    
    # Create model
    model = BetaVAE(latent_dim=20, beta=4.0)
    
    # Test with random input in [0, 1] range
    batch_size = 32
    x = torch.rand(batch_size, 28, 28)
    
    # Forward pass
    recon_x, mu, logvar, z = model(x)
    
    print(f"Input shape: {x.shape}")
    print(f"Latent shape: {z.shape}")
    print(f"Reconstruction shape: {recon_x.shape}")
    print(f"Compression ratio: {model.get_compression_ratio():.2f}x")
    
    # Test loss
    loss, recon_loss, kl_loss = model.loss_function(recon_x, x, mu, logvar)
    print(f"Total loss: {loss.item():.2f}")
    print(f"Reconstruction loss: {recon_loss.item():.2f}")
    print(f"KL loss: {kl_loss.item():.2f}")
    
    # Test model creation
    latent_dims = [8, 16, 32]
    betas = [1.0, 4.0, 10.0]
    models = create_beta_vae_models(latent_dims, betas)
    print(f"\nCreated {len(models)} model configurations")
    for i, config in enumerate(models[:3]):
        print(f"Model {i+1}: latent_dim={config['latent_dim']}, beta={config['beta']}, compression={config['compression_ratio']:.2f}x")
