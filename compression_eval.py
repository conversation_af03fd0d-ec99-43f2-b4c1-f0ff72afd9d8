import torch
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import json
import os
from tqdm import tqdm

from mnist_loader import get_sample_images
from beta_vae import BetaVAE

def calculate_mse(original, reconstructed):
    """Calculate Mean Squared Error."""
    return np.mean((original - reconstructed) ** 2)

def calculate_ssim(original, reconstructed):
    """Calculate Structural Similarity Index."""
    # Ensure images are in the right format for SSIM
    if len(original.shape) == 3:
        # For batch of images, calculate average SSIM
        ssim_values = []
        for i in range(original.shape[0]):
            ssim_val = ssim(original[i], reconstructed[i], data_range=1.0)
            ssim_values.append(ssim_val)
        return np.mean(ssim_values)
    else:
        return ssim(original, reconstructed, data_range=1.0)

def calculate_psnr(original, reconstructed):
    """Calculate Peak Signal-to-Noise Ratio."""
    if len(original.shape) == 3:
        # For batch of images, calculate average PSNR
        psnr_values = []
        for i in range(original.shape[0]):
            psnr_val = psnr(original[i], reconstructed[i], data_range=1.0)
            psnr_values.append(psnr_val)
        return np.mean(psnr_values)
    else:
        return psnr(original, reconstructed, data_range=1.0)

def estimate_compression_bits(model, images, device='cpu'):
    """Estimate the number of bits needed to represent compressed images."""
    model.eval()
    model.to(device)
    
    with torch.no_grad():
        if isinstance(images, np.ndarray):
            images = torch.FloatTensor(images).to(device)
        
        # Encode to latent space
        mu, logvar = model.encoder(images)
        
        # For compression estimation, we use the mean (mu) as the compressed representation
        # In practice, this would be quantized, but we'll estimate based on the latent dimension
        
        # Calculate bits per latent dimension (assuming 8-bit quantization)
        bits_per_dimension = 8
        total_bits = model.latent_dim * bits_per_dimension
        
        # Add overhead for storing variance information (simplified)
        overhead_bits = model.latent_dim * 2  # 2 bits per dimension for variance
        
        total_compressed_bits = total_bits + overhead_bits
        
        # Original image bits
        original_bits = 28 * 28 * 8  # 8 bits per pixel
        
        compression_ratio = original_bits / total_compressed_bits
        
        return {
            'compressed_bits': total_compressed_bits,
            'original_bits': original_bits,
            'compression_ratio': compression_ratio,
            'bits_per_pixel': total_compressed_bits / (28 * 28)
        }

def evaluate_model_quality(model, images, device='cpu'):
    """Evaluate reconstruction quality of a model."""
    model.eval()
    model.to(device)
    
    with torch.no_grad():
        if isinstance(images, np.ndarray):
            images_tensor = torch.FloatTensor(images).to(device)
        else:
            images_tensor = images.to(device)
        
        # Get reconstructions
        reconstructed, _, _, _ = model(images_tensor)
        
        # Convert back to numpy for evaluation
        if isinstance(images, np.ndarray):
            original_np = images
        else:
            original_np = images.cpu().numpy()
        
        reconstructed_np = reconstructed.cpu().numpy()
        
        # Calculate metrics
        mse = calculate_mse(original_np, reconstructed_np)
        ssim_score = calculate_ssim(original_np, reconstructed_np)
        psnr_score = calculate_psnr(original_np, reconstructed_np)
        
        return {
            'mse': float(mse),
            'ssim': float(ssim_score),
            'psnr': float(psnr_score),
            'reconstructions': reconstructed_np
        }

def create_distortion_curve_data(model_results_path, sample_images, device='cpu'):
    """Create data for distortion-complexity curves."""
    
    # Load training results
    with open(model_results_path, 'r') as f:
        results = json.load(f)
    
    curve_data = []
    
    print("Evaluating models for distortion curves...")
    
    for result in tqdm(results):
        model_path = result['model_path']
        
        # Load model
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint['config']
        
        model = BetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Evaluate compression
        compression_info = estimate_compression_bits(model, sample_images, device)
        
        # Evaluate quality
        quality_info = evaluate_model_quality(model, sample_images, device)
        
        # Combine data
        curve_point = {
            'model_id': result['model_id'],
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': compression_info['compression_ratio'],
            'bits_per_pixel': compression_info['bits_per_pixel'],
            'compressed_bits': compression_info['compressed_bits'],
            'mse': quality_info['mse'],
            'ssim': quality_info['ssim'],
            'psnr': quality_info['psnr'],
            'train_recon_loss': result['final_recon_loss'],
            'test_recon_loss': result['test_recon_loss']
        }
        
        curve_data.append(curve_point)
    
    return curve_data

def generate_sample_reconstructions(model_results_path, sample_images, sample_labels, 
                                  num_examples=20, device='cpu'):
    """Generate reconstructions for visualization."""
    
    # Load training results
    with open(model_results_path, 'r') as f:
        results = json.load(f)
    
    # Select a few representative models
    # Sort by compression ratio and select evenly spaced models
    results_sorted = sorted(results, key=lambda x: x['compression_ratio'])
    selected_indices = np.linspace(0, len(results_sorted)-1, 10).astype(int)
    selected_results = [results_sorted[i] for i in selected_indices]
    
    reconstructions_data = []
    
    print("Generating sample reconstructions...")
    
    for result in tqdm(selected_results):
        model_path = result['model_path']
        
        # Load model
        checkpoint = torch.load(model_path, map_location=device)
        config = checkpoint['config']
        
        model = BetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # Get reconstructions for sample images
        quality_info = evaluate_model_quality(model, sample_images[:num_examples], device)
        
        reconstruction_data = {
            'model_id': result['model_id'],
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': result['compression_ratio'],
            'original_images': sample_images[:num_examples].tolist(),
            'reconstructed_images': quality_info['reconstructions'].tolist(),
            'labels': sample_labels[:num_examples].tolist(),
            'mse': quality_info['mse'],
            'ssim': quality_info['ssim'],
            'psnr': quality_info['psnr']
        }
        
        reconstructions_data.append(reconstruction_data)
    
    return reconstructions_data

def main():
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Get sample images for evaluation
    print("Loading sample images...")
    sample_images, sample_labels = get_sample_images(num_samples=100)
    
    # Create distortion curve data
    model_results_path = 'models/training_results.json'
    if os.path.exists(model_results_path):
        print("Creating distortion curve data...")
        curve_data = create_distortion_curve_data(model_results_path, sample_images, device)
        
        # Save curve data
        with open('models/distortion_curves.json', 'w') as f:
            json.dump(curve_data, f, indent=2)
        
        print("Generating sample reconstructions...")
        reconstructions_data = generate_sample_reconstructions(
            model_results_path, sample_images, sample_labels, device=device
        )
        
        # Save reconstructions data
        with open('models/sample_reconstructions.json', 'w') as f:
            json.dump(reconstructions_data, f, indent=2)
        
        print("Evaluation complete!")
        print(f"Curve data saved to models/distortion_curves.json")
        print(f"Reconstructions saved to models/sample_reconstructions.json")
        
        # Print summary
        compression_ratios = [d['compression_ratio'] for d in curve_data]
        mse_values = [d['mse'] for d in curve_data]
        ssim_values = [d['ssim'] for d in curve_data]
        
        print(f"\nSummary:")
        print(f"Compression ratios: {min(compression_ratios):.2f}x to {max(compression_ratios):.2f}x")
        print(f"MSE range: {min(mse_values):.6f} to {max(mse_values):.6f}")
        print(f"SSIM range: {min(ssim_values):.4f} to {max(ssim_values):.4f}")
        
    else:
        print(f"Training results not found at {model_results_path}")
        print("Please run train_vae.py first to train the models.")

if __name__ == "__main__":
    main()
