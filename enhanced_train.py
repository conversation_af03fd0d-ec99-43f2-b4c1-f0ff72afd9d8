import torch
import torch.optim as optim
import numpy as np
import os
import json
from tqdm import tqdm
import matplotlib.pyplot as plt

from mnist_loader import load_mnist_data
from enhanced_vae import EnhancedBetaVAE, create_enhanced_models

def train_enhanced_model(model, train_loader, num_epochs=25, learning_rate=1e-3, device='cpu'):
    """Train a single Enhanced Beta-VAE model with improved training strategy."""
    model.to(device)
    optimizer = optim.Adam(model.parameters(), lr=learning_rate, weight_decay=1e-5)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=3, factor=0.5)
    
    model.train()
    train_losses = []
    
    for epoch in range(num_epochs):
        epoch_loss = 0
        epoch_recon_loss = 0
        epoch_kl_loss = 0
        
        for batch_idx, (data, _) in enumerate(train_loader):
            data = data.to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            recon_batch, mu, logvar, _ = model(data)
            
            # Compute loss with step information for capacity scheduling
            step = epoch * len(train_loader) + batch_idx
            loss, recon_loss, kl_loss = model.loss_function(recon_batch, data, mu, logvar, step)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping for stability
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            epoch_loss += loss.item()
            epoch_recon_loss += recon_loss.item()
            epoch_kl_loss += kl_loss.item()
        
        avg_loss = epoch_loss / len(train_loader.dataset)
        avg_recon_loss = epoch_recon_loss / len(train_loader.dataset)
        avg_kl_loss = epoch_kl_loss / len(train_loader.dataset)
        
        # Learning rate scheduling
        scheduler.step(avg_loss)
        
        train_losses.append({
            'epoch': epoch,
            'total_loss': avg_loss,
            'recon_loss': avg_recon_loss,
            'kl_loss': avg_kl_loss,
            'capacity': model.capacity
        })
        
        if epoch % 5 == 0:
            print(f'Epoch {epoch:3d}: Loss={avg_loss:.4f}, Recon={avg_recon_loss:.4f}, KL={avg_kl_loss:.4f}, Cap={model.capacity:.2f}')
    
    return train_losses

def evaluate_enhanced_model(model, test_loader, device='cpu'):
    """Evaluate enhanced model performance on test set."""
    model.eval()
    total_loss = 0
    total_recon_loss = 0
    total_kl_loss = 0
    
    with torch.no_grad():
        for data, _ in test_loader:
            data = data.to(device)
            recon_batch, mu, logvar, _ = model(data)
            loss, recon_loss, kl_loss = model.loss_function(recon_batch, data, mu, logvar)
            
            total_loss += loss.item()
            total_recon_loss += recon_loss.item()
            total_kl_loss += kl_loss.item()
    
    avg_loss = total_loss / len(test_loader.dataset)
    avg_recon_loss = total_recon_loss / len(test_loader.dataset)
    avg_kl_loss = total_kl_loss / len(test_loader.dataset)
    
    return {
        'total_loss': avg_loss,
        'recon_loss': avg_recon_loss,
        'kl_loss': avg_kl_loss
    }

def train_enhanced_models(num_models=500, device='cpu', save_dir='enhanced_models'):
    """Train 500 enhanced models with different configurations."""
    os.makedirs(save_dir, exist_ok=True)
    
    # Load data
    print("Loading MNIST data...")
    train_loader, test_loader = load_mnist_data(batch_size=64)  # Smaller batch for stability
    
    # Generate configurations
    print(f"Generating {num_models} enhanced model configurations...")
    configurations = create_enhanced_models(num_models=num_models)
    
    # Sort configurations by compression ratio for better training order
    configurations.sort(key=lambda x: EnhancedBetaVAE(latent_dim=x['latent_dim']).get_compression_ratio())
    
    results = []
    
    for i, config in enumerate(tqdm(configurations, desc="Training enhanced models")):
        print(f"\nTraining model {i+1}/{len(configurations)}")
        print(f"Configuration: latent_dim={config['latent_dim']}, beta={config['beta']:.3f}")
        
        # Create model
        model = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        compression_ratio = model.get_compression_ratio()
        
        print(f"Target compression ratio: {compression_ratio:.1f}x")
        
        # Adjust training epochs based on complexity
        if compression_ratio > 100:
            epochs = 30  # More epochs for high compression
        elif compression_ratio > 50:
            epochs = 25
        else:
            epochs = 20
        
        # Train model
        train_losses = train_enhanced_model(model, train_loader, num_epochs=epochs, device=device)
        
        # Evaluate model
        test_metrics = evaluate_enhanced_model(model, test_loader, device=device)
        
        # Save model
        model_path = os.path.join(save_dir, f'enhanced_model_{i:03d}.pth')
        torch.save({
            'model_state_dict': model.state_dict(),
            'config': config,
            'train_losses': train_losses,
            'test_metrics': test_metrics,
            'compression_ratio': compression_ratio
        }, model_path)
        
        # Store results
        result = {
            'model_id': i,
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': compression_ratio,
            'final_train_loss': train_losses[-1]['total_loss'],
            'final_recon_loss': train_losses[-1]['recon_loss'],
            'final_kl_loss': train_losses[-1]['kl_loss'],
            'test_loss': test_metrics['total_loss'],
            'test_recon_loss': test_metrics['recon_loss'],
            'test_kl_loss': test_metrics['kl_loss'],
            'model_path': model_path,
            'final_capacity': train_losses[-1]['capacity']
        }
        results.append(result)
        
        print(f"Compression ratio: {compression_ratio:.1f}x")
        print(f"Test reconstruction loss: {test_metrics['recon_loss']:.4f}")
        
        # Save intermediate results every 50 models
        if (i + 1) % 50 == 0:
            intermediate_path = os.path.join(save_dir, f'training_results_checkpoint_{i+1}.json')
            with open(intermediate_path, 'w') as f:
                json.dump(results, f, indent=2)
            print(f"Checkpoint saved: {intermediate_path}")
    
    # Save final results
    results_path = os.path.join(save_dir, 'enhanced_training_results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\nEnhanced training complete! Results saved to {results_path}")
    return results

def quick_enhanced_test(num_models=10):
    """Quick test with fewer enhanced models."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    print("Loading MNIST data...")
    train_loader, test_loader = load_mnist_data(batch_size=32)
    
    # Generate test configurations
    print(f"Generating {num_models} test configurations...")
    configurations = create_enhanced_models(num_models=num_models)
    
    results = []
    
    for i, config in enumerate(tqdm(configurations, desc="Testing enhanced models")):
        print(f"\nTesting model {i+1}/{len(configurations)}")
        print(f"Configuration: latent_dim={config['latent_dim']}, beta={config['beta']:.3f}")
        
        # Create model
        model = EnhancedBetaVAE(latent_dim=config['latent_dim'], beta=config['beta'])
        compression_ratio = model.get_compression_ratio()
        
        # Quick training (fewer epochs)
        train_losses = train_enhanced_model(model, train_loader, num_epochs=5, device=device)
        test_metrics = evaluate_enhanced_model(model, test_loader, device=device)
        
        result = {
            'model_id': i,
            'latent_dim': config['latent_dim'],
            'beta': config['beta'],
            'compression_ratio': compression_ratio,
            'test_recon_loss': test_metrics['recon_loss']
        }
        results.append(result)
        
        print(f"Compression: {compression_ratio:.1f}x, Test loss: {test_metrics['recon_loss']:.4f}")
    
    return results

def main():
    """Main training function."""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Ask user for training mode
    print("\nChoose training mode:")
    print("1. Quick test (10 models, 5 epochs each)")
    print("2. Full training (500 models, 20-30 epochs each)")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    if choice == "1":
        print("Running quick test...")
        results = quick_enhanced_test(num_models=10)
        print(f"\nQuick test completed with {len(results)} models")
        
        # Show compression range
        compression_ratios = [r['compression_ratio'] for r in results]
        print(f"Compression range: {min(compression_ratios):.1f}x to {max(compression_ratios):.1f}x")
        
    elif choice == "2":
        print("Starting full training...")
        results = train_enhanced_models(num_models=500, device=device)
        print(f"\nFull training completed with {len(results)} models")
        
        # Show summary
        compression_ratios = [r['compression_ratio'] for r in results]
        recon_losses = [r['test_recon_loss'] for r in results]
        print(f"Compression range: {min(compression_ratios):.1f}x to {max(compression_ratios):.1f}x")
        print(f"Reconstruction loss range: {min(recon_losses):.4f} to {max(recon_losses):.4f}")
        
    else:
        print("Invalid choice. Exiting.")

if __name__ == "__main__":
    main()
